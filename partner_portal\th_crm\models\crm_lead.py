from odoo import fields, models, api, _
import json
from odoo.exceptions import ValidationError, AccessError
import xmlrpc.client
from datetime import datetime, timedelta
from markupsafe import Markup
from odoo.exceptions import UserError

PARTNER_FIELDS_TO_SYNC = [
    'lang',
    'mobile',
    'title',
    'function',
    'website',
]

# Subset of partner fields: sync all or none to avoid mixed addresses
PARTNER_ADDRESS_FIELDS_TO_SYNC = [
    'street',
    'street2',
    'city',
    'zip',
    'state_id',
    'country_id',
    'th_ward_id',
    'th_district_id',
    'th_ethnicity_id',
    'th_religion_id',
    'th_gender',
    'th_birthday',
    'th_place_of_birth_id',
    'th_street',
    'th_ward_permanent_id',
    'th_district_permanent_id',
    'th_state_id',
    'th_country_id',
]

READONLY_STATES = {
    # 'transfer': [('readonly', True)],
}


class CrmLead(models.Model):
    _inherit = "crm.lead"

    readonly_domain = fields.Char(compute="_compute_readonly_domain")
    name_id_sequence = fields.Char()
    email_from = fields.Char(readonly=True, tracking=True)
    phone = fields.Char(readonly=True, tracking=True)
    name = fields.Char(required=False, default="MỚI", copy=False)
    partner_id = fields.Many2one(string="Sinh viên", tracking=True)
    street = fields.Char(store=False, compute_sudo=True, tracking=True, readonly=True)
    street2 = fields.Char(store=False)
    zip = fields.Char(store=False, readonly=True)
    city = fields.Char(store=False, readonly=True)
    state_id = fields.Many2one(readonly=True, store=False, compute_sudo=True, tracking=True)
    country_id = fields.Many2one(readonly=True, store=True, compute_sudo=True, tracking=True)
    title = fields.Many2one(store=False, tracking=True, readonly=True)
    function = fields.Char(store=False, tracking=True, readonly=True)
    contact_name = fields.Char(store=False)
    stage_id = fields.Many2one(string="Mối quan hệ", domain="[('th_type', '=', 'crm'),('th_auto', '=', False)]",
                               group_expand="", ondelete='set null')
    user_id = fields.Many2one(string="Đối tác phụ trách", domain="th_domain_user_id", default=False, tracking=True, states=READONLY_STATES)
    team_id = fields.Many2one(string="Đội tư vấn",
                              domain="[('use_opportunities', '=', True),('th_origin_id', '=', th_origin_id)]",
                              tracking=True, states=READONLY_STATES)
    tag_ids = fields.Many2many(string="Nhóm cơ hội", states=READONLY_STATES)

    th_description = fields.Text(string="Mô tả", tracking=True)
    th_last_check = fields.Datetime(string="Liên hệ lần cuối", tracking=True, copy=False)

    th_customer_code = fields.Char(string="Mã Khách hàng", related="partner_id.th_customer_code", readonly=True,
                                   tracking=True)
    th_gender = fields.Selection(string="Giới tính", selection=[('male', 'Nam'), ('female', 'Nữ'), ('other', 'Khác'), ],
                                 compute="_compute_partner_address_values", store=False,
                                 compute_sudo=True, tracking=True, readonly=True)
    th_birthday = fields.Date(string="Ngày sinh", compute="_compute_partner_address_values",
                              store=False, compute_sudo=True, tracking=True, readonly=True)
    th_place_of_birth_id = fields.Many2one(comodel_name="res.country.state", string="Nơi sinh",
                                           compute="_compute_partner_address_values",
                                           store=False, compute_sudo=True,
                                           tracking=True, readonly=True)
    th_ward_id = fields.Many2one(comodel_name='th.country.ward', string='Phường/ Xã',
                                 compute="_compute_partner_address_values", readonly=True,
                                 domain="[('th_district_id', '=?', th_district_id), ('th_district_id.th_state_id', '=?', state_id)]",
                                 store=False, compute_sudo=True, tracking=True)
    th_district_id = fields.Many2one(comodel_name='th.country.district', string='Quận/ Huyện',
                                     domain="[('th_state_id', '=?', state_id)]",
                                     compute="_compute_partner_address_values",
                                     readonly=True, store=False, compute_sudo=True, tracking=True)
    th_phone2 = fields.Char(string="Số điện thoại 2", compute='_compute_th_phone2',
                            readonly=True, store=True, tracking=True)
    th_ethnicity_id = fields.Many2one(comodel_name="th.ethnicity", string="Dân tộc",
                                      compute="_compute_partner_address_values",
                                      store=False, compute_sudo=True, tracking=True, readonly=True)
    th_religion_id = fields.Many2one(comodel_name="th.religion", string="Tôn giáo",
                                     compute="_compute_partner_address_values",
                                     store=False, compute_sudo=True, tracking=True, readonly=True)
    th_channel_id = fields.Many2one(comodel_name="th.info.channel", string="Kênh", tracking=True, states=READONLY_STATES)
    th_source_name = fields.Char(string="Tên nguồn", tracking=True, copy=False)
    th_source_group_id = fields.Many2one(comodel_name="th.source.group", string="Nhóm nguồn", tracking=True, copy=False)
    th_admissions_station_id = fields.Many2one(comodel_name="th.admissions.station", string="Trạm tuyển sinh",
                                               tracking=True, copy=False, states=READONLY_STATES)
    th_admissions_region_id = fields.Many2one(comodel_name="th.admissions.region", string="Vùng tuyển sinh",
                                              tracking=True, copy=False, states=READONLY_STATES)
    th_registration_date = fields.Date(string="Ngày đăng ký", tracking=True, copy=False, readonly=True)
    th_level_up_date = fields.Date(string="Ngày lên Level", tracking=True, copy=False,
                                   compute='_compute_th_level_up_date', store=True, default=False)
    th_status_group_id = fields.Many2one(comodel_name="th.status.category", string="Nhóm tình trạng",
                                         domain='th_domain_status_group_id', tracking=True, copy=False, states=READONLY_STATES )
    th_domain_status_group_id = fields.Char(compute="_compute_th_domain_status_group_id")
    th_status_detail_id = fields.Many2one(comodel_name="th.status.detail",
                                          domain="[('th_status_category_id', '=?', th_status_group_id),th_domain_status_group_id]",
                                          string="Trạng thái chi tiết", tracking=True, copy=False, states=READONLY_STATES)
    th_domain_th_call_status = fields.Char(string="Call domain", compute="_compute_th_domain_th_call_status")
    th_major_ids = fields.Many2many(comodel_name="th.major", string="Ngành đăng ký", copy=False, states=READONLY_STATES)
    th_major_id = fields.Many2one(comodel_name="th.major", string="Ngành đăng ký", copy=False, states=READONLY_STATES)
    th_graduation_system_id = fields.Many2one(comodel_name="th.graduation.system", string="Hệ tốt nghiệp",
                                              tracking=True, copy=False, states=READONLY_STATES)
    th_partner_referred_id = fields.Many2one(comodel_name="res.partner", string="Partner giới thiệu", tracking=True, copy=False)
    th_affiliate_code = fields.Char(string="Mã Tiếp Thị Liên Kết", readonly=True,
                                    related='th_partner_referred_id.th_affiliate_code', tracking=True)
    # th_permanent_residence = fields.Char(string="Hộ khẩu thường trú")
    th_reuse_source = fields.Char(string="Tái sử dụng CSKH", tracking=True, copy=False)
    th_reuse = fields.Char(string="Tái sử dụng TVTS", copy=False, tracking=True)

    th_storage = fields.Boolean(string="Lưu trữ", default=False)
    th_uuid_mail_channel = fields.Char('uuid mail.channel')
    th_ownership_id = fields.Many2one(comodel_name="th.ownership.unit", string="Đơn vị sở hữu", tracking=True, states=READONLY_STATES)
    th_origin_id = fields.Many2one(comodel_name="th.origin", string="Trường", tracking=True, states=READONLY_STATES)
    th_domain_stage_id = fields.Many2many(comodel_name="crm.stage", string="", compute="_compute_th_domain_stage_id")
    th_tuition_handed = fields.Selection(selection=[('full', 'Đủ'), ('partial', 'Một phần')],
                                         string="Thanh toán học phí", copy=False)
    th_fees = fields.Boolean(string="Đã thanh toán lệ phí", copy=False)
    th_admission_decision = fields.Boolean(string="Đã trúng tuyển", copy=False)
    # th_decision_id = fields.Many2one("th.admission.decision", string="Danh sách quyết định")
    th_street = fields.Char(string="Địa chỉ (Hộ khẩu)", compute="_compute_partner_address_values", tracking=True,
                            readonly=True)
    th_ward_permanent_id = fields.Many2one(comodel_name='th.country.ward', string='Phường/ Xã (Hộ khẩu)',
                                           domain="[('th_district_id', '=?', th_district_permanent_id), ('th_district_id.th_state_id', '=?', th_state_id)]",
                                           compute="_compute_partner_address_values",
                                           readonly=True, tracking=True)
    th_district_permanent_id = fields.Many2one(comodel_name='th.country.district', string='Quận/ Huyện (Hộ khẩu)',
                                               compute="_compute_partner_address_values",
                                               domain="[('th_state_id', '=?', th_state_id)]", readonly=True,
                                               tracking=True)
    th_state_id = fields.Many2one("res.country.state", string='Tỉnh/ TP (Hộ khẩu)', ondelete='restrict',
                                  compute="_compute_partner_address_values", readonly=True,
                                  domain="[('country_id', '=?', th_country_id)]", tracking=True)
    th_country_id = fields.Many2one('res.country', string='Quốc gia (Hộ khẩu)', ondelete='restrict',
                                    compute="_compute_partner_address_values",
                                    readonly=True, tracking=True)
    th_profile_batches_id = fields.Many2one(comodel_name="th.profile.batches", string="Đợt bàn giao", copy=False)
    th_student_code = fields.Char(string="Mã sinh viên", copy=False)
    th_result = fields.Selection(selection=[('keep', 'Giữ'), ('transfer', 'Chuyển')], string="Kết quả xử lý",
                                 copy=False)
    th_is_a_duplicate_opportunity = fields.Boolean(string="Là cơ hội trùng lặp", default=False, copy=False)
    th_key_duplicate = fields.Boolean(string="Key duplicate", copy=False)
    th_lead_duplicate_id = fields.Many2one(comodel_name="crm.lead", copy=False)
    th_admission_list_id = fields.Many2one(comodel_name="th.admission.list", copy=False, ondelete='cascade')
    th_crm_code = fields.Char()
    th_check_admission = fields.Boolean("Chờ xét tuyển", copy=False)
    th_resolve_duplicate = fields.Boolean(copy=False, string='Xử lý trùng')
    th_domain_user_id = fields.Char(compute="_compute_th_domain_user")
    th_lead_aff_id = fields.Integer(string='Cơ hội bên aff', copy=False)
    th_lead_crm_samp_id = fields.Integer(string='Id cơ hội samp', copy=False)
    th_hide_action_confirm_crm = fields.Boolean(default=False)
    th_crm_job = fields.Char(string="Nghề nghiệp", copy=False, tracking=True, states=READONLY_STATES)
    th_stage_auto = fields.Boolean(string="Trạng thái tự động", related='stage_id.th_auto')
    th_withdraw_profile_id = fields.Integer(string="ID Mối quan hệ trước khi tạo HS")
    th_opportunity_list_partner_crm_ids = fields.One2many("crm.lead.opportunity.list.partner", "crm_lead_id",
                                                          copy=True,
                                                          compute="_compute_th_opportunity_list_partner_crm_ids")
    th_is_refunded_tuition = fields.Boolean(string="Là cơ hội đã hoàn học phí", copy=False)
    th_is_refund_tuition = fields.Boolean(string="Là cơ hội cần hoàn học phí", copy=False)
    th_acceptance = fields.Char('Quyết định trúng tuyển')
    th_class = fields.Char('Khóa')
    th_class_detail = fields.Char('Lớp chuyên ngành')

    th_duplicate_processed_lead = fields.Boolean(string="Cơ hội đã xử lý trùng", copy=False, default=False)
    th_duplicate_description = fields.Text(string="Mô tả kiểm tra trùng", copy=False)
    th_duplicate_date = fields.Date(string="Ngày kiểm tra trùng", copy=False)
    th_duplicate_type = fields.Selection(
        selection=[('manual', 'Thủ công'), ('auto', 'Tự động'), ('need_handle', 'Cần xử lý'),
                   ('no_results', 'Chưa có điều kiện')], string="Loại xử lý", copy=False, default='auto')

    th_code_getfly = fields.Char(string="Mã KH Getfly", copy=False)
    th_check_crm_phone = fields.Char(string="Điện thoại")
    th_check_crm_email = fields.Char(string="Email")
    state = fields.Selection(selection=[('keep', 'Đối tác tự chăm'), ('transfer', 'Tư vấn chăm')], string="Loại chăm sóc", default=False)
    th_person_in_charge = fields.Char(string="Người phụ trách")
    th_dup_state = fields.Selection(string='Trạng thái', selection=[('processing', 'Đang xử lý'), ('processed', 'Đã xử lý')], default=False, tracking=True)
    th_selection_dup_result = fields.Selection(selection=[('keep', 'Trùng-Giữ'), ('change', 'Trùng-Chuyển')], string="Kết quả", default=False, tracking=True)
    th_crm_lead_source_id = fields.Integer('id của Lead Góc CRM-SamP')
    th_crm_team_samp_id = fields.Many2one('th.team.samp', 'Đội nhóm AUM chăm sóc')
    th_duplicated_date = fields.Date(string="Ngày bị trùng", copy=False)
    th_delivery_date = fields.Date(string="Ngày bàn giao", copy=False, tracking=True)
    th_dividing_ring_id = fields.Many2one('th.dividing.ring', 'Vòng chia')
    th_domain_dividing = fields.Char(default=[], compute="_compute_th_domain_dividing")
    th_domain_own = fields.Char(compute="_compute_th_domain_own")
    th_utm_source = fields.Char('utm source', copy=False)
    th_utm_medium = fields.Char('utm medium', copy=False)
    th_utm_campaign = fields.Char('utm campaign', copy=False)
    th_utm_term = fields.Char('utm term', copy=False)
    th_utm_content = fields.Char('utm content', copy=False)
    th_domain_user_referred = fields.Char(compute="_compute_th_domain_user_ref")
    th_user_referred_id = fields.Many2one(comodel_name="res.users", string="Người giới thiệu", tracking=True, copy=False)
    th_student_profile_id = fields.Many2one(comodel_name="th.student.profile", string="Hồ sơ", tracking=True, copy=False)
    th_uuid_form = fields.Char('uuid form')
    th_form_name = fields.Char('Form name')
    th_partner_domain = fields.Char(compute="_compute_th_partner_domain", default=json.dumps([('id', 'in', [])]))
    th_required_fill = fields.Boolean(related="stage_id.th_required_fill", string="Bắt buộc điền")
    th_log_note_check = fields.Boolean()
    th_self_lead = fields.Boolean(string="Cơ hội tự chốt", tracking=True)
    th_check_admin_crm = fields.Boolean(string="Check tài khoản admin CRM", compute="_compute_th_check_admin_crm")
    th_training_system_id = fields.Many2one(comodel_name='th.training.system', string='Hệ đào tạo')
    th_crm_tags_ids = fields.Many2many(comodel_name="crm.tag", string="Nhóm cơ hội")
    def _compute_readonly_domain(self):
        for rec in self:
            if rec.state == 'transfer' or rec.th_delivery_date:
                rec.readonly_domain = json.dumps([])
            else:
                rec.readonly_domain = False
    def unlink(self):
        for lead in self:
            # Chỉ cho phép xóa nếu là đối tác tự chăm và chưa bàn giao
            if lead.state != 'keep' or lead.th_delivery_date:
                raise UserError(_("Cơ hội đã bàn giao, không thể xóa cơ hội."))

        return super(CrmLead, self).unlink()

    def _compute_th_check_admin_crm(self):
        for rec in self:
            rec.th_check_admin_crm = False
            if self.env.user.has_group('th_crm.th_group_admin_crm'):
                rec.th_check_admin_crm = True

    @api.depends("th_check_crm_phone", 'th_check_crm_email')
    def _compute_th_partner_domain(self):
        domain = []
        for rec in self:
            partner_ids = self.env['res.partner'].search(
                [('th_module_ids.name', 'in', ['CRM']), '|', '|', '|', ('phone', '=', rec.th_check_crm_phone or 'khong co [phone] '),
                 ('th_phone2', '=', rec.th_check_crm_phone or 'khong co [phone] '),
                 ('th_phone2', '=', rec.th_check_crm_phone or 'khong co [phone] '),
                 ('email', '=', rec.th_check_crm_email or ' ko cos [email] ')
                 ]).ids
            if partner_ids:
                domain.append(('id', 'in', partner_ids))
            else:
                domain.append(('id', 'in', []))
            rec.th_partner_domain = json.dumps(domain)

    @api.onchange('th_user_referred_id')
    def onchange_th_partner_referred_id(self):
        for rec in self:
            if rec.th_user_referred_id:
                rec.th_partner_referred_id = rec.th_user_referred_id.partner_id.id
            else:
                rec.th_partner_referred_id = False

    @api.depends("th_origin_id", 'stage_id')
    def _compute_th_domain_dividing(self):
        for rec in self:
            domain = []
            th_dividing_ids = self.env['th.dividing.ring'].search([('th_origin_id', '=', rec.th_origin_id.id), ('th_is_partner_dividing', '=', True)]).ids
            if th_dividing_ids:
                domain.append(('id', 'in', th_dividing_ids))
            else:
                domain.append(('id', 'in', []))
            rec.th_domain_dividing = json.dumps(domain)

    @api.depends('stage_id')
    def _compute_th_domain_own(self):
        for rec in self:
            domain = []
            th_ownership_id = self.env['th.ownership.unit'].search([]).filtered(lambda l: self.env.uid in l.th_user_ids.ids)
            if th_ownership_id:
                domain.append(('id', 'in', th_ownership_id.ids))
            else:
                domain.append(('id', 'in', []))
            rec.th_domain_own = json.dumps(domain)

    @api.depends('th_ownership_id')
    def _compute_th_domain_user_ref(self):
        for rec in self:
            domain = []
            if rec.th_ownership_id:
                domain.append(('id', 'in', rec.th_ownership_id.th_user_ids.ids))
            else:
                domain.append(('id', 'in', []))
            rec.th_domain_user_referred = json.dumps(domain)

    @api.model
    def default_get(self, field_list):
        rec = super(CrmLead, self).default_get(field_list)
        th_ownership_id = self.env['th.ownership.unit'].search([]).filtered(lambda l: self.env.uid in l.th_user_ids.ids).sorted(key=lambda r: r.id)[:1]
        rec['th_ownership_id'] = th_ownership_id.id if th_ownership_id else self.env.ref('th_setup_parameters.th_aum_ownership_unit').id
        return rec

    @api.onchange('state')
    def onchange_team_samp(self):
        for rec in self:
            if rec.state != 'transfer':
                rec.th_dividing_ring_id = False
            if rec.state == 'transfer' and not rec.th_delivery_date:
                rec.th_delivery_date = fields.Datetime.now()
            else:
                rec.th_delivery_date = False

    @api.onchange('th_check_crm_phone')
    def onchange_th_check_crm_phone(self):
        self.ensure_one()
        if self.th_check_crm_phone:
            partner = self.env['res.partner'].search(
                ['|', ('phone', '=', self.th_check_crm_phone), ('th_phone2', '=', self.th_check_crm_phone)], limit=1)
            if partner:
                #     duplicate_lead = self.env['crm.lead'].sudo().search(
                #         [('partner_id', '=', partner.id), ('partner_id', '!=', False),
                #          ('th_ownership_id', '=', self.th_ownership_id.id)], limit=1)
                #     if duplicate_lead:
                #     error_message = _(
                #         "Trùng cơ hội với tên đối tác '%s', Số điện thoại '%s', Số điện thoại 2 '%s', Email '%s', Người phụ trách '%s'!") % (
                #                         duplicate_lead.th_partner_id.name, duplicate_lead.th_partner_phone or '',
                #                         duplicate_lead.th_partner_phone2 or '',
                #                         duplicate_lead.th_partner_email or '', duplicate_lead.th_user_id.name or '')
                #     raise UserError(error_message)
                # else:
                self.partner_id = partner

    @api.onchange('th_check_crm_email')
    def onchange_th_check_crm_email(self):
        self.ensure_one()
        if self.th_check_crm_email:
            partner = self.env['res.partner'].search([('email', '=', self.th_check_crm_email)], limit=1)
            if partner:
                self.partner_id = partner

    def action_withdraw_profile(self):
        if self.th_withdraw_profile_id:
            self.stage_id = self.th_withdraw_profile_id
            # self.th_student_profile_id.unlink()
            # self.th_student_profile_id = False
            self.th_auto_next_level()

    def _compute_phone(self):
        res = super(CrmLead, self)._compute_phone()
        for rec in self:
            if rec.partner_id and rec.partner_id.phone != rec.phone:
                rec.phone = False
        return res

    def _compute_email_from(self):
        res = super(CrmLead, self)._compute_email_from()
        for rec in self:
            if rec.partner_id and rec.partner_id.email != rec.email_from:
                rec.email_from = False
        return res

    @api.depends('stage_id')
    def _compute_th_domain_stage_id(self):
        data = self.env['crm.stage'].search([('th_auto', '=', False), ('th_type', '=', 'crm')]).ids
        for rec in self:
            if rec.stage_id and rec.stage_id.id not in data:
                data.append(rec.stage_id.id)
            rec.th_domain_stage_id = [(6, 0, data)]

    @api.depends('team_id')
    def _compute_th_domain_user(self):
        for rec in self:
            domain = []
            if rec.team_id:
                team_member_ids = self.env['crm.team'].search([('id', '=', rec.team_id.id)]).member_ids.ids
                domain.append(('id', 'in', team_member_ids))
            rec.th_domain_user_id = json.dumps(domain)

    @api.depends('stage_id')
    def _compute_th_domain_status_group_id(self):
        for rec in self:
            domain = []
            if rec.stage_id:
                status_group_ids = self.env['th.status.category'].search(
                    [('th_type', '=', 'crm'), ('th_crm_level_ids', '=', rec.stage_id.id)])
                domain.append(('id', 'in', status_group_ids.ids))
            rec.th_domain_status_group_id = json.dumps(domain)

    @api.onchange('th_ward_id')
    def onchange_th_ward_id(self):
        if self.th_ward_id:
            self.th_district_id = self.th_ward_id.th_district_id.id
            self.state_id = self.th_district_id.th_state_id.id
            self.country_id = self.state_id.country_id.id

    @api.onchange('user_id')
    def onchange_user_id(self):
        if self.user_id and self.user_id.id in self.env['crm.team'].search([]).mapped('member_ids').ids:
            count = self.env['crm.team'].search_count([('member_ids', '=', self.user_id.id)])
            if count == 1:
                self.team_id = self.env['crm.team'].search([('member_ids', '=', self.user_id.id)])
            elif count > 1:
                if not self.team_id:
                    self.team_id = self.env['crm.team'].search([('member_ids', '=', self.user_id.id)], limit=1)
                return {'domain': {
                    'team_id': [('id', 'in', self.env['crm.team'].search([('member_ids', '=', self.user_id.id)]).ids)]}}
        else:
            self.team_id = False

    def _prepare_address_values_from_partner(self, partner):
        # Sync all address fields from partner, or none, to avoid mixing them.
        if partner and any(partner[f] for f in PARTNER_ADDRESS_FIELDS_TO_SYNC):
            values = {f: partner[f] for f in PARTNER_ADDRESS_FIELDS_TO_SYNC}
        else:
            values = {f: self[f] for f in PARTNER_ADDRESS_FIELDS_TO_SYNC}
        return values

    @api.depends('partner_id')
    def _compute_partner_address_values(self):
        """ Sync all or none of address fields """
        for lead in self:
            lead.update(lead._prepare_address_values_from_partner(lead.partner_id))

    def _get_partner_phone2_update(self):
        """Calculate if we should write the phone on the related partner. When
        the phone of the lead / partner is an empty string, we force it to False
        to not propagate a False on an empty string.

        Done in a separate method so it can be used in both ribbon and inverse
        and compute of phone update methods.
        """
        self.ensure_one()
        if self.partner_id and self.th_phone2 != self.partner_id.th_phone2:
            lead_phone_formatted = self.phone_get_sanitized_number(number_fname='th_phone2') or self.th_phone2 or False
            partner_phone_formatted = self.partner_id.phone_get_sanitized_number(
                number_fname='th_phone2') or self.partner_id.th_phone2 or False
            return lead_phone_formatted != partner_phone_formatted
        return False

    @api.onchange('th_status_group_id')
    def onchange_th_status_group_id(self):
        if self.th_status_detail_id and self.th_status_detail_id.th_status_category_id != self.th_status_group_id:
            self.th_status_detail_id = False

    @api.depends('stage_id', 'th_status_group_id')
    def _compute_th_domain_th_call_status(self):
        for rec in self:
            domain = []
            if rec.stage_id and rec.th_status_group_id:
                status_detail_ids = self.env['th.status.category'].search(
                    [('id', '=', rec.th_status_group_id.id), ('th_type', '=', 'crm')]).mapped(
                    'th_status_detail_ids').filtered(lambda u: rec.stage_id.id in u.th_crm_level_ids.ids)
                domain.append(('id', 'in', status_detail_ids.ids))
            rec.th_domain_th_call_status = json.dumps(domain)

    @api.depends('partner_id.th_phone2')
    def _compute_th_phone2(self):
        for lead in self:
            if lead.partner_id.th_phone2 and lead._get_partner_phone2_update():
                lead.th_phone2 = lead.partner_id.th_phone2
            elif lead.partner_id and lead.partner_id.th_phone2 != lead.th_phone2:
                lead.th_phone2 = False

    def write(self, values):
        for rec in self:
            if rec.state == 'transfer':
                values.pop('th_last_check', None)
        if self._context.get('th_test_import', False):
            return super(CrmLead, self.with_context(mail_create_nosubscribe=True, mail_post_autofollow=False)).write(values)
        admin_group = self.env.user.has_group('th_crm.th_group_admin_crm')
        for rec in self:
            if rec.state == 'transfer' and not admin_group and values.get('stage_id', False):
                raise ValidationError('Bạn không có quyền chỉnh sửa khi cơ hội được bàn giao cho AUM!')
            if self._context.get('note', False) and rec.state == 'transfer':
                return True

            if (rec.th_dup_state == 'processing' or rec.th_selection_dup_result == 'change') and not admin_group:
                print('Không thể chỉnh sửa được cơ hội bị trùng hoặc đã chuyển đơn vị sở hữu!')
            th_old_state = True if rec.state == 'keep' or values.get('state', False) == 'keep' else False

        if self._context.get('manual') and self._context.get('uid') not in [1, 2]:
            values['th_duplicate_type'] = 'manual'
        if 'description' in values and values.get('description') or 'th_status_detail_id' in values and values.get('th_status_detail_id'):
            values['th_last_check'] = fields.Date.today()
        # if values.get('team_id') and self._context.get('reuse'):
        #     for rec in self:
        #         values['user_id'] = self.env['crm.team'].browse(values.get('team_id')).action_assign_leads(rec)[
        #             values.get('team_id')]
        # th_old_state = True if self.state == 'keep' or values.get('state', False) == 'keep' else False
        res = super(CrmLead, self.with_context(mail_create_nosubscribe=True, mail_post_autofollow=False)).write(values)
        for rec in self:
            # if rec.stage_id.is_won:
            #     rec.th_date_close = fields.Date.today()

            if values.get('th_dup_state', False) and not values.get('th_duplicated_date', False):
                rec.th_duplicated_date = datetime.now()
            if values.get('partner_id') or values.get('th_origin_id'):
                L7A1 = self.env.ref('th_crm.th_stage_lead20').id
                L7A2 = self.env.ref('th_crm.th_stage_lead21').id
                L7A3 = self.env.ref('th_crm.th_stage_lead22').id
                L8 = self.env.ref('th_crm.th_stage_lead23').id
                state = [L7A1, L7A2, L7A3, L8]
                exist_lead = self.env['crm.lead'].search(
                    [('id', '!=', rec.id),
                     ('th_origin_id', '=', rec.th_origin_id.id),
                     ('partner_id', '=', rec.partner_id.id),
                     ('th_is_a_duplicate_opportunity', '=', False), ('th_ownership_id', '=', rec.th_ownership_id.id)],
                    limit=1)

                if exist_lead:
                    msg = ("Cơ hội này đã trùng với cơ hội %s" % exist_lead.name)
                    self.message_post(body=msg)
                    # self.th_is_a_duplicate_opportunity = True
                    self.env['bus.bus']._sendone(
                        self.env.user.partner_id,
                        "simple_notification",
                        {
                            "title": "Warning",
                            "message": msg,
                            "warning": False
                        }
                    )

        keys = [
            'th_ownership_id', 'th_origin_id', 'th_source_name', 'th_description', 'th_status_detail_id',
            'th_channel_id', 'th_source_group_id', 'stage_id', 'state', 'th_major_id', 'th_status_group_id', 'phone',
            'th_phone2', 'th_admissions_station_id', 'th_admissions_region_id',
            'th_graduation_system_id', 'th_dividing_ring_id', 'th_partner_referred_id', 'th_crm_job',
        ]

        # if list(filter(lambda x: x in keys, list(values.keys()))) and not self._context.get('th_test_import', False):
        #     for rec in self:
        #         self.with_context({'write': True}).th_crud_api_lead(rec)
        return res

    def action_open_profile(self):
        self.ensure_one()
        return {
            'name': 'Hồ sơ',
            'view_mode': 'form',
            'res_model': 'th.student.profile',
            'type': 'ir.actions.act_window',
            'target': 'new',
            'context': {'default_th_lead_id': self.id, 'no_create': False},
            'res_id': self.th_student_profile_id.id,
            # 'domain': [('id', '=', self.th_student_profile_id.id)],
        }

    def change_admission(self):
        for rec in self:
            rec.th_check_admission = True
            if rec.th_auto_next_level():
                rec.stage_id = rec.th_auto_next_level().th_crm_level.id
        return True

    def th_action_archive(self):
        for rec in self:
            rec.th_storage = True
        return {
            'type': 'ir.actions.client',
            'tag': 'reload',
        }

    def action_create_reuse(self):
        context = self.env.context.copy()
        context.update({'default_th_create_reuse': True,
                        'default_th_invisible_university': True})
        self.env['crm.lead.reuse'].browse(self._context.get('active_id')).write({'th_create_reuse': True,
                                                                                 'th_invisible_university': True})
        return {
            'name': 'Điều kiện chia cơ hội',
            'view_mode': 'form',
            'res_model': 'crm.lead.reuse',
            'type': 'ir.actions.act_window',
            'target': 'new',
            'res_id': self._context.get('active_id'),
            'context': context,
        }

    @api.model_create_multi
    def create(self, vals_list):
        if self._context.get('th_test_import', False):
            return super(CrmLead, self.with_context(mail_create_nosubscribe=True, mail_post_autofollow=False)).create(vals_list)
        for vals in vals_list:
            if not vals.get('th_origin_id', False):
                vals['th_origin_id'] = self.env.ref('th_setup_parameters.th_aum_university_origin').id
            if not vals.get('user_id', False):
                vals['user_id'] = self.env.user.id
            if not vals.get('th_last_check', False):
                vals['th_last_check'] = fields.Datetime.now()
            if self._context.get('web_form', False):
                vals['th_channel_id'] = self.env.ref('th_setup_parameters.th_aum_info_channel_web').id
            if self._context.get('livechat', False):
                vals['th_channel_id'] = self.env.ref('th_setup_parameters.th_aum_info_channel_chat_web').id
            vals['th_crm_code'] = self.env['ir.sequence'].next_by_code('crm.code')
            if vals.get('th_partner_referred_id'):
                vals['th_user_referred_id'] = self.env['res.partner'].browse(vals['th_partner_referred_id']).user_id

            if not vals.get('th_registration_date', False):
                vals['th_registration_date'] = fields.Date.context_today(self)

        res = super(CrmLead, self.with_context(mail_create_nosubscribe=True, mail_post_autofollow=False)).create(vals_list)
        if self._context.get('th_test_import', False):
            return res

        for rec in res:
            won_stage_id = self.env['crm.stage'].search([('is_won', '=', True)], limit=1).id
            exist_lead = self.env['crm.lead'].search(
                [('id', '!=', rec.id),
                 ('th_origin_id', '=', rec.th_origin_id.id),
                 ('partner_id', '=', rec.partner_id.id),
                 ('th_is_a_duplicate_opportunity', '=', False),
                 ('th_ownership_id', '=', rec.th_ownership_id.id),
                 ], limit=1)

            if exist_lead and not self._context.get('th_test_import', False) and exist_lead.stage_id.id != won_stage_id:
                raise ValidationError("Cơ hội này đã trùng với cơ hội %s" % exist_lead.name)

            elif exist_lead and exist_lead.stage_id.id == won_stage_id and not self._context.get('th_test_import', False):
                if exist_lead.th_major_id and exist_lead.th_major_id.id == rec.th_major_id.id:
                    msg = ('Khách hàng %s - Ngành %s đã trùng với cơ hội %s đạt L8, vui lòng xem lại!' % (
                        rec.partner_id.name, rec.th_major_id.name, exist_lead.name))
                    rec.message_post(body=msg)
                    rec.with_context(th_test_import=True).th_is_a_duplicate_opportunity = True
                    self.env['bus.bus']._sendone(
                        self.env.user.partner_id,
                        "simple_notification",
                        {
                            "title": "Warning",
                            "message": msg,
                            "warning": False
                        })

            elif exist_lead and self._context.get('th_test_import', False):
                print("Cơ hội này đã trùng với cơ hội %s" % exist_lead.name)
                rec.th_duplicated_date = datetime.now()
                # return False

            # if not self._context.get('th_test_import', False) and not self._context.get('th_test_import', False):
            #     lead_samp = self.sudo().with_delay().th_crud_api_lead(rec)

            # rec.name = self._context.get('name', rec.name)
            # if rec.name == 'MỚI':
            #     rec.name_id_sequence = name_id
            #     rec.name = "[CRM" + name_id + "]" + "-" + rec.partner_id.name
        return res

    # def th_crud_api_lead(self, crm_lead=None):
    #     values = {}
    #     if not crm_lead:
    #         return False
    #     server_api = self.env['th.api.server'].search([('state', '=', 'deploy'), ('th_type', '=', 'samp')], limit=1, order='id desc')
    #     try:
    #         if not server_api:
    #             raise ValidationError('Không tìm thấy server!')
    #         context = {
    #             'aff_create': True,
    #             'uuid_form': crm_lead.th_uuid_form,
    #         }
    #         partner = crm_lead.partner_id
    #         if crm_lead.th_partner_referred_id:
    #             context['th_affiliate_code']: crm_lead.th_partner_referred_id.th_affiliate_code
    #
    #         values = {
    #             'th_ownership_id': crm_lead.th_ownership_id.th_own_samp_id if crm_lead.th_ownership_id.th_own_samp_id else False,
    #             'th_origin_id': crm_lead.th_origin_id.th_origin_samp_id if crm_lead.th_origin_id.th_origin_samp_id else False,
    #             'th_source_name': crm_lead.th_source_name if crm_lead.th_source_name else False,
    #             'th_description': crm_lead.th_description if crm_lead.th_description else False,
    #             'th_major_id': crm_lead.th_major_id.th_major_samp_id if crm_lead.th_major_id else False,
    #             'type': 'opportunity',
    #             'th_status_group_id': crm_lead.th_status_group_id.th_s_c_samp_id if crm_lead.th_status_group_id.th_s_c_samp_id else False,
    #             'th_status_detail_id': crm_lead.th_status_detail_id.th_s_c_d_samp_id if crm_lead.th_status_detail_id.th_s_c_d_samp_id else False,
    #             'th_channel_id': crm_lead.th_channel_id.th_channel_samp_id if crm_lead.th_channel_id.th_channel_samp_id else False,
    #             'th_source_group_id': crm_lead.th_source_group_id.th_source_samp_id if crm_lead.th_source_group_id.th_source_samp_id else False,
    #             'stage_id': crm_lead.stage_id.th_stage_samp_id if crm_lead.stage_id.th_stage_samp_id else False,
    #             'th_crm_lead_b2b_id': crm_lead.id,
    #             'state': crm_lead.state if crm_lead.state else False,
    #             'th_last_check': crm_lead.th_last_check if crm_lead.th_last_check else False,
    #             'th_admissions_station_id': crm_lead.th_admissions_station_id.th_station_samp_id if crm_lead.th_admissions_station_id.th_station_samp_id else False,
    #             'th_admissions_region_id': crm_lead.th_admissions_region_id.th_region_samp_id if crm_lead.th_admissions_region_id.th_region_samp_id else False,
    #             'th_graduation_system_id': crm_lead.th_graduation_system_id.th_graduation_samp_id if crm_lead.th_graduation_system_id.th_graduation_samp_id else False,
    #             'th_dividing_ring_id': crm_lead.th_dividing_ring_id.th_dividing_ring_samp_id if crm_lead.th_dividing_ring_id else False,
    #             'th_crm_job': crm_lead.th_crm_job,
    #             'th_utm_source': crm_lead.th_utm_source,
    #             'th_utm_medium': crm_lead.th_utm_medium,
    #             'th_utm_campaign': crm_lead.th_utm_campaign,
    #             'th_utm_term': crm_lead.th_utm_term,
    #             'th_utm_content': crm_lead.th_utm_content,
    #             'th_form_name': crm_lead.th_form_name,
    #         }
    #
    #         if not crm_lead.partner_id.th_affiliate_code or partner.th_partner_samp_id == 0:
    #             if not partner.id:
    #                 return False
    #             context['new_partner'] = True
    #             context['partner_name'] = partner.name
    #             context['phone'] = partner.phone if partner.phone else False
    #             context['th_phone2'] = partner.th_phone2 if partner.th_phone2 else False
    #             context['email'] = partner.email if partner.email else False
    #             context['partner_aff_id'] = partner.id
    #         else:
    #             values['partner_id'] = partner.th_partner_samp_id
    #
    #         result_apis = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(server_api.th_url_api))
    #         db = server_api.th_db_api
    #         uid_api = server_api.th_uid_api
    #         password = server_api.th_password
    #
    #         th_lead_aff = result_apis.execute_kw(db, uid_api, password, 'crm.lead', 'search', [[['id', '=', crm_lead.th_lead_crm_samp_id]]])
    #         if not th_lead_aff and crm_lead.th_lead_crm_samp_id:
    #             return True
    #
    #         if not self._context.get('write', False):
    #             data = result_apis.execute_kw(db, uid_api, password, 'crm.lead', 'th_action_crm_c1', [[], values], {'context': context})
    #             if data:
    #                 # partner_refer = self.env['res.partner'].sudo().search([('th_affiliate_code', '=', data.get('th_affiliate_code'))], limit=1).id
    #                 crm_lead.with_context(th_test_import=True).write({
    #                     'th_lead_crm_samp_id': data.get('th_lead_crm_samp_id', False) if data.get('th_lead_crm_samp_id', False) else data.get('crm_id'),
    #                     'th_person_in_charge': data.get('th_person_in_charge', False),
    #                     'th_dup_state': data.get('th_dup_state', False),
    #                     'th_selection_dup_result': data.get('th_selection_dup_result', False),
    #                     'name': data.get('name', False) if data.get('name', False) else "Mới",
    #                     'th_is_a_duplicate_opportunity': True if data.get('th_selection_dup_result', False) == 'change' or data.get('th_dup_state', False) == 'processing' else False,
    #                     'th_crm_lead_source_id': data.get('th_crm_lead_source_id', False),
    #                     # 'th_partner_referred_id': partner_refer,
    #                     'th_user_referred_id': partner.user_id.id,
    #                     'type': 'opportunity',
    #                     'state': data.get('state', False),
    #                     'th_status_group_id': data.get('th_status_group_id', False),
    #                     'th_status_detail_id': data.get('th_status_detail_id', False),
    #                     # 'th_partner_referred_id': partner_refer,
    #                 })
    #                 p_values = {'th_partner_samp_id': data.get('partner_id')}
    #                 if not partner.th_customer_code or not partner.th_affiliate_code:
    #                     p_values['th_customer_code'] = data.get('th_customer_code')
    #                     p_values['th_affiliate_code'] = data.get('th_affiliate_code')
    #                 partner.with_context(th_test_import=True).write(p_values)
    #                 return True
    #         else:
    #             if not crm_lead.th_lead_crm_samp_id:
    #                 return False
    #             context['write'] = True
    #             context['id'] = crm_lead.th_lead_crm_samp_id
    #             context['phone'] = crm_lead.phone
    #             context['th_phone2'] = crm_lead.th_phone2
    #             context['email_from'] = crm_lead.email_from
    #             data = result_apis.execute_kw(db, uid_api, password, 'crm.lead', 'th_action_crm_c1', [[], values], {'context': context})
    #             if data:
    #                 crm_lead.with_context(th_test_import=True).write({
    #                     'th_person_in_charge': data.get('th_person_in_charge', False),
    #                 })
    #                 return True
    #     except Exception as e:
    #         print(e)
    #         self.env['th.log.api'].create({
    #             'state': 'error',
    #             'th_model': str(self._name),
    #             'th_description': str(e),
    #             'th_record_id': str(crm_lead.ids),
    #             'th_input_data': str(values),
    #             'th_function_call': str('th_crud_api_lead'),
    #         })
    #         self.env['bus.bus']._sendone(
    #             self.env.user.partner_id,
    #             "simple_notification",
    #             {
    #                 "title": "Warning",
    #                 "message": "Không thể kết nối sang CRM tổng vui lòng chờ ít phút hoặc báo cho IT để khắc phục sự cố!",
    #                 'sticky': False,
    #                 "warning": False
    #             }
    #         )
    #         return False
    #         # raise ValidationError('Không thể đẩy cơ hội sang CRM tổng lưu lại sau ít phút hoặc báo cho IT để khắc phục sự cố!')

    def update_th_last_check(self):
        self.update({'th_last_check': fields.Datetime.now()})

    def th_create_activities(self):
        view_id = self.env.ref('mail.mail_activity_view_form_popup').id
        model = self.env['ir.model'].search([('model', '=', 'crm.lead')])
        return {
            'name': 'Lịch làm việc',
            'view_mode': 'form',
            'res_model': 'mail.activity',
            'type': 'ir.actions.act_window',
            'views': [[view_id, 'form']],
            'target': 'new',
            'context': {'default_res_model_id': model.id,
                        'default_res_id': self[0].id,
                        'default_user_id': self[0].user_id.id,
                        'create_activities': True}
        }

    def th_auto_next_level(self):
        for rec in self:
            domain = []
            th_type_condition = 'no_order'
            accounts = self.env['account.move'].search([('th_crm_lead_id', '=', self.id)])
            # sale_order = self.env['sale.order'].search([('opportunity_id', '=', self.id)])
            level = self.env['th.level.condition']
            # kiểm tra hóa đơn
            if not accounts:
                th_type_condition = 'no_order'
            else:
                if any(accounts.filtered(lambda d: d.payment_state == 'paid')):
                    th_type_condition = 'handed_over_full'
                if any(accounts.filtered(lambda d: d.payment_state == 'partial')):
                    th_type_condition = 'handed_over_partial_tuition'
                if any(accounts.filtered(lambda d: d.payment_state == 'not_paid')):
                    th_type_condition = 'no_order'
            if rec.order_ids.th_paid_order_compute == False:
                if any(accounts.filtered(lambda d: d.payment_state in ['paid', 'partial'])):
                    th_type_condition = 'handed_over_partial_tuition'
            if accounts and (sum(accounts.mapped('th_receive_amount')) - sum(accounts.mapped('th_refund_amount'))) <= 0:
                th_type_condition = 'no_order'
            if rec.order_ids.th_paid_order_compute == True and (
                    not any(accounts.filtered(lambda d: d.move_type == 'in_invoice')) or (
                    sum(accounts.mapped('th_receive_amount')) - sum(accounts.mapped('th_refund_amount'))) >= sum(
                    rec.order_ids.mapped('amount_total'))):
                th_type_condition = 'handed_over_full'
            domain.append(('th_type_condition', '=', th_type_condition))

            if rec.th_admission_decision:
                domain.append(('th_admission_decision', '=', True))
            else:
                domain.append(('th_admission_decision', '=', False))
            level |= level.search(domain, limit=1)
            return level


    @api.model
    def receive_data_from_module(self, data_to_send):
        check_phone = self.env['res.partner'].search(
            [('phone', '=', data_to_send.get('th_phone_number')), ('email', '=', data_to_send.get('th_mail'))],
            limit=1)
        affiliate_code = self.env['res.partner'].search([('name', '=', data_to_send.get('th_affiliate_code'))], limit=1)
        if check_phone:
            self.env['crm.lead'].create([{'partner_id': check_phone.id,
                                          'th_description': data_to_send.get('th_description'),
                                          'th_partner_referred_id': affiliate_code.id, }])
        elif not check_phone:
            id_partner = self.env['res.partner'].create([{'name': data_to_send.get('name'),
                                                          'phone': data_to_send.get('th_phone_number'),
                                                          'email': data_to_send.get('th_mail')}])

            self.env['crm.lead'].create([{'partner_id': id_partner.id,
                                          'th_description': data_to_send.get('th_description'),
                                          'th_partner_referred_id': affiliate_code.id, }])

    @api.onchange('stage_id')
    def onchange_status_group(self):
        for rec in self:
            if rec.state == 'transfer' or rec.th_is_a_duplicate_opportunity:
                raise ValidationError('Lead đã bàn giao hoặc bị trùng không có quyền chỉnh sửa trạng thái!')
            if rec.th_status_group_id not in rec.env['th.status.category'].search(
                    [('th_type', '=', 'crm'), ('th_crm_level_ids', '=', rec.stage_id.id)]):
                rec.th_status_group_id = False

    @api.depends('stage_id')
    def _compute_th_level_up_date(self):
        for rec in self:
            rec.th_level_up_date = fields.Date.today()

    def action_open_profile_customer(self):
        self.ensure_one()
        context = {}
        if self.state == 'transfer':
            context.update({'create': 0, 'edit': 1, 'delete': 0})

        return {
            'name': 'Hồ sơ',
            'view_mode': 'form',
            'res_model': 'res.partner',
            'type': 'ir.actions.act_window',
            'target': 'new',
            'res_id': self.partner_id.id,
            'domain': [('id', '=', self.partner_id.id)],
            'context': context,
        }

    @api.depends('partner_id')
    def _compute_th_opportunity_list_partner_crm_ids(self):
        for record in self:
            list_ids = []
            record.th_opportunity_list_partner_crm_ids = False
            if record.partner_id:
                opportunities = self.env['crm.lead'].search([
                    ('partner_id', '=', record.partner_id.id),
                    ('th_origin_id', '!=', record.th_origin_id.id),
                    ('th_is_a_duplicate_opportunity', '!=', True), ])
                for rec in opportunities:
                    opportunity_list_id = self.env['crm.lead.opportunity.list.partner'].create({
                        'name': rec.name if rec.name else "Mới",
                        'th_partner_id': rec.partner_id.id,
                        'th_last_check': rec.th_last_check,
                        'th_origin_id': rec.th_origin_id.id,
                        'th_stage_id': rec.stage_id.id
                    })
                    list_ids.append(opportunity_list_id.id)
                record.th_opportunity_list_partner_crm_ids = [(6, 0, list_ids)]

    def action_view_history(self):
        self.ensure_one()
        # call api
        val = {
            'name': self.name,
            'th_type_sync': 'ontime',
            'th_internal_id': self.id,
            'th_data': False,
            'th_model_name': 'th.care.history',
            'th_system': 'samp',
        }
        self.sudo().th_func_create_or_update(val)

        return {
            'name': 'Lịch sử chăm sóc',
            'view_mode': 'tree,form',
            'res_model': "th.care.history",
            'type': 'ir.actions.act_window',
            'target': 'target',
            'domain': [('th_crm_lead_new_id', '=', self.id)],
            'context': [],
        }

    def th_send_noti_duplicate_lead(self):
        self.ensure_one()
        duplicate = self.env['th.duplicate.lead.notify.wr'].search([('new_lead_id', '=', self.id)])
        if not duplicate:
            return {
                'name': 'Khiếu nại',
                'view_mode': 'form',
                'res_model': "th.duplicate.lead.notify.wr",
                'type': 'ir.actions.act_window',
                'target': 'new',
                'context': {'default_new_lead_id': self.id}
            }
        else:
            return {
                'name': 'Khiếu nại',
                'view_mode': 'form',
                'res_model': "th.duplicate.lead.notify.wr",
                'type': 'ir.actions.act_window',
                'target': 'new',
                'res_id': duplicate.id,
                'context': {'default_new_lead_id': self.id, 'send':True}
            }

    def _message_add_suggested_recipient(self, result, partner=None, email=None, lang=None, reason=''):
        """ Called by _message_get_suggested_recipients, to add a suggested
            recipient in the result dictionary. The form is :
                partner_id, partner_name<partner_email> or partner_name, reason """
        result = super()._message_add_suggested_recipient(result, partner=partner, email=email, lang=lang,
                                                          reason=reason)
        if self._name == 'crm.lead':
            for res in range(len(result.get(self.id))):
                if result.get(self.id)[res][0] == self.partner_id.id:
                    del result.get(self.id)[res]
        return result

    def th_action_hand_over(self):
        for rec in self:
            if not rec.th_dividing_ring_id:
                raise ValidationError('Trường "Vòng chia" để tự động chia cho tư vấn chăm!')
            rec.state = 'transfer'
            rec.th_delivery_date = fields.Datetime.now()

    def th_action_hand_over_choose_team(self):
        return {
            'name': 'Chọn đội nhóm',
            'view_mode': 'form',
            'res_model': "crm.lead",
            'type': 'ir.actions.act_window',
            'views': [(self.env.ref('th_crm.th_crm_popup_view_form').id, 'form')],
            'target': 'new',
            'context': {'default_new_lead_id': self.id},
            'res_id': self.id
        }

    def th_accept_team(self):
        for rec in self:
            rec.th_dividing_ring_id = rec.th_dividing_ring_id
            rec.state = 'transfer'
            rec.th_delivery_date = fields.Datetime.now()

    def th_write_condition(self, data=None):
        if not data:
            return False
        try:

            for rec in data:
                search_data = self.search([('id', '=', rec[0])])
                if not search_data:
                    continue
                if rec[1].get('th_dup_state', False) == 'processing':
                    search_data.sudo().with_context(th_test_import=True).write(rec[1])
                else:
                    if rec[1].get('th_selection_dup_result', False) == 'change':
                        rec[1]['th_lead_crm_samp_id'] = False
                        rec[1]['th_is_a_duplicate_opportunity'] = True if rec[1].get('th_selection_dup_result', False) == 'change' or rec[1].get('th_dup_state', False) == 'processing' else False
                        search_data.sudo().with_context(th_test_import=True).write(rec[1])
                    if rec[1].get('th_selection_dup_result', False) == 'keep':
                        search_data.sudo().with_context(th_test_import=True).write(rec[1])
            return True
        except Exception as e:
            print(e)
            return False

    # def th_update_value_again_crm(self):
    #     for rec in self:
    #         if rec.id and not rec.th_lead_crm_samp_id:
    #             self.th_crud_api_lead(rec)

    def th_create_crm_c1_sch(self, datas):
        data_return = []
        record = False
        data_for = False
        partner_id = False
        try:
            for rec in datas:
                data_for = rec[1]
                domain = [
                    '|', '|', ('phone', '=', rec[1].get('phone')), ('th_phone2', '=', rec[1].get('phone')),
                    '|', '|', ('phone', '=', rec[1].get('phone2', ' ko có ')),
                    ('th_phone2', '=', rec[1].get('phone2', ' ko có ')),
                    ('email', '=', rec[1].get('email', ' ko có '))
                ]
                if rec[0] == 0:

                    if not rec[1].get('partner_id', False):
                        partner_id = self.env['res.partner'].sudo().search(domain, limit=1)
                        rec[1]['partner_id'] = self.env['res.partner'].with_context(th_test_import=True).create(rec[2]).id if not partner_id else partner_id.id

                    if not rec[1].get('th_source_group_id', False) and rec[3].get('name', False):
                        rec[1]['th_source_group_id'] = self.env['th.source.group'].create(rec[3]).id
                    if not rec[1].get('th_dividing_ring_id', False) and rec[4].get('name', False):
                        rec[1]['th_dividing_ring_id'] = self.env['th.dividing.ring'].create(rec[4]).id

                    result = self.env['crm.lead'].with_context(th_test_import=True).sudo().create([rec[1]])
                    data_return.append([result.th_lead_crm_samp_id, {'th_crm_lead_b2b_id': result.id},
                                        {'th_partner_aff_id': result.partner_id.id},
                                        {'th_source_aff_id': result.th_source_group_id.id},
                                        {'th_dividing_ring_b2b_id': result.th_dividing_ring_id.id},
                                        ])
                else:
                    record = self.browse(rec[0])
                    print(record)
                    if not record.exists():
                        continue
                    if not rec[1].get('partner_id', False):
                        # continue
                        partner_id = self.env['res.partner'].sudo().search(domain, limit=1)
                        if partner_id:
                            partner_id.with_context(th_test_import=True).sudo().write(rec[2])
                        else:
                            partner_id = self.env['res.partner'].create(rec[2]).id
                        rec[1]['partner_id'] = partner_id

                    if not rec[1].get('th_source_group_id', False) and rec[3].get('name', False):
                        rec[1]['th_source_group_id'] = self.env['th.source.group'].create(rec[3]).id
                    if not rec[1].get('th_dividing_ring_id', False) and rec[4].get('name', False):
                        rec[1]['th_dividing_ring_id'] = self.env['th.dividing.ring'].create(rec[4]).id
                    if record.th_dup_state:
                        rec[1]['th_dup_state'] = record.th_dup_state
                    record.with_context(th_test_import=True).sudo().write(rec[1])
                    data_return.append([record.th_lead_crm_samp_id, {'th_crm_lead_b2b_id': record.id},
                                        {'th_partner_aff_id': record.partner_id.id},
                                        {'th_source_aff_id': record.th_source_group_id.id},
                                        {'th_dividing_ring_b2b_id': record.th_dividing_ring_id.id},
                                        ])
        except Exception as e:
            print(e)
            self.env['th.log.api'].create({
                'state': 'error',
                'th_model': str(self._name),
                'th_description': str(e),
                'th_record_id': str(record),
                'th_input_data': str(data_for),
                'th_function_call': str('th_create_crm_c1_sch'),
            })

        return data_return

    def create_lead_form(self, vals=None):
        try:
            th_source_group_id = False
            th_partner_referred_id = self.env['res.partner']
            check_module = self.env['ir.config_parameter'].sudo().get_param('th_check_partner_module')

            # Tìm hoặc tạo th_source_group_id
            utm_source = vals.get('th_utm_source')
            if utm_source:
                source_group = self.env['th.source.group'].search([('name', '=', utm_source)], limit=1)
                th_source_group_id = source_group.id if source_group else self.env['th.source.group'].create(
                    {'name': utm_source}).id

            # Tìm th_partner_referred_id
            affiliate_code = vals.get('th_affiliate_code')
            if affiliate_code:
                th_partner_referred_id = self.env['res.partner'].search([('th_affiliate_code', '=', affiliate_code)],
                                                                        limit=1)

            # Xây dựng domain để tìm partner
            domain = []
            th_phone = vals.get('th_phone')
            th_email = vals.get('th_email')
            if th_phone:
                domain = ['|', ('phone', '=', th_phone), ('th_phone2', '=', th_phone)]
            if th_email:
                domain = ['|'] + domain + [('email', '=', th_email)] if domain else [('email', '=', th_email)]
            if check_module:
                domain.append(('th_module_ids', 'in', self.env.ref('th_setup_parameters.th_crm_module').ids))

            # Tìm hoặc tạo partner
            partner_aff = self.env['res.partner'].search(domain, limit=1) if domain else self.env['res.partner']
            if partner_aff:
                if th_phone:
                    if partner_aff.phone and partner_aff.phone != th_phone and not partner_aff.th_phone2:
                        partner_aff.write({'th_phone2': th_phone})
                    elif not partner_aff.phone:
                        partner_aff.write({'phone': th_phone})
                    elif partner_aff.th_phone2 and partner_aff.phone and th_phone not in [partner_aff.th_phone2,
                                                                                          partner_aff.phone]:
                        partner_aff.write({
                            'comment': Markup((partner_aff.comment or '') + ' Số điện thoại mới: ' + th_phone)
                        })
            else:
                partner_aff = self.env['res.partner'].create({
                    'name': vals.get('th_customer'),
                    'phone': th_phone,
                    'email': th_email,
                    'th_module_ids': [(4, self.env.ref('th_setup_parameters.th_module_crm').id)],
                })

            # Tạo mới cơ hội
            new_opportunity = {
                'partner_id': partner_aff.id,
                'th_origin_id': vals.get('th_origin') or self.env.ref(
                    'th_setup_parameters.th_aum_university_origin').id,
                'th_last_check': fields.Datetime.now(),
                'th_partner_referred_id': th_partner_referred_id.id if th_partner_referred_id else False,
                'th_lead_aff_id': vals.get('id'),
                'th_ownership_id': vals.get('th_ownership_id'),
                'th_description': vals.get('th_description'),
                'th_source_name': vals.get('th_source_name'),
                'th_utm_source': vals.get('th_utm_source'),
                'th_utm_medium': vals.get('th_utm_medium'),
                'th_utm_campaign': vals.get('th_utm_campaign'),
                'th_utm_term': vals.get('th_utm_term'),
                'th_utm_content': vals.get('th_utm_content'),
                'th_source_group_id': th_source_group_id,
                'th_uuid_form': vals.get('th_uuid_form'),
                'th_form_name': vals.get('th_form_name'),
                'type': 'lead',
            }
            self.env['crm.lead'].sudo().create([new_opportunity])
        except Exception as e:
            print(e)

