from odoo import api, fields, models
from odoo.exceptions import ValidationError


class ThTrainingSystem(models.Model):
    _name = 'th.training.system'
    _description = 'Hệ Đào Tạo CRM'

    name = fields.Char(string="Hệ đào tạo",required=True)
    th_description = fields.Text(string="<PERSON>ô tả")

    @api.constrains('name')
    def _constraint_name(self):
        if any(self.search([('id', '!=', rec.id), ('name', '=', rec.name)]) for rec in self):
            raise ValidationError("Tên hệ đào tạo không được trùng, kiểm tra lại!")