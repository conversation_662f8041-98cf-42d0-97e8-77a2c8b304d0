<odoo>
    <record id="th_training_system_view_tree" model="ir.ui.view">
        <field name="name">th_training_system_view_tree</field>
        <field name="model">th.training.system</field>
        <field name="arch" type="xml">
            <tree string="">
                <field name="name"/>
                <field name="th_description"/>
            </tree>
        </field>
    </record>

    <record id="th_training_system_view_form" model="ir.ui.view">
        <field name="name">th_training_system_view_form</field>
        <field name="model">th.training.system</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="th_description"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="th_training_system_view_act" model="ir.actions.act_window">
        <field name="name">H<PERSON></field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">th.training.system</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
          <p class="oe_view_nocontent_create">
            <!-- Add Text Here -->
          </p><p>
            <!-- More details about what a user can do with this object will be OK -->
          </p>
        </field>
    </record>
</odoo>
