# -*- coding: utf-8 -*-
{
    'name': 'ABS SYNC FastAPI',
    'author': "TH Company",
    'summary': 'ABS widget',
    'category': 'AUM Business System/ Odoo SYNC FastAPI',
    "website": "",
    'description': "",
    "version": "********.19112024.1",
    'license': 'LGPL-3',
    'depends': [
        'th_fastapi',
        'th_srm',
        'th_crm',
        'th_setup_parameters',
        'base_automation',
        'mail'
    ],
    'data': [
        "data/res_partner_title.xml",
        "data/ir_cron_mail_message.xml",
        "data/ir_cron_res_partner.xml",
        "data/ir_cron_sale_order.xml",
        "data/ir_cron_th_student.xml",
        "data/ir_cron_th_apm.xml",
        "data/ir_cron_daily_sync.xml",
        "data/mail_message.xml",
        "data/th_apm.xml",
        "data/th_crm_automation.xml",
        "data/th_apm_automation.xml",
        "data/th_srm_automation.xml",
        "data/student_profile.xml",
        "data/parammeters.xml",
        "data/th_country.xml",
        "data/th_res_partner_automation.xml",
        "data/th_info_channel.xml",
        "data/th_crm_paratmeters_automation.xml",
        "data/configuration_apm_automation.xml",
        "data/th_product_automation.xml",
        "data/th_srm_configuration.xml",
        "data/th_apm_level_automation.xml",
        "data/th_apm_team.xml",
        "data/th_apm_sale_order_automation.xml",
        "data/ir_cron_th_crm.xml",
        "data/ir_cron_th_apm.xml",
        "security/ir.model.access.csv",
        "views/th_clipboard.xml",
        "views/th_mapping.xml",
        "views/menu.xml",
    ],
    'qweb': [],
    'installable': True,
    'application': True,
    'auto_install': False,
}