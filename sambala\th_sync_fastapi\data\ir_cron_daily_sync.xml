<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="0">
    <!-- Scheduled action cho đồng bộ hóa hàng ngày các trường CRM từ SamP sang B2B -->
    <record id="th_schedule_sync_fastapi_crm_daily" model="ir.cron">
        <field name="name">Schedule sync CRM Daily Fields</field>
        <field name="model_id" ref="model_th_clipboard"/>
        <field name="state">code</field>
        <field name="code">model.search([('th_model_name', '=', 'crm.lead'), ('th_type_sync', '=', 'a_day'), ('th_status', '=', 'waiting'), ('th_system', '=', 'b2b')]).schedule_sync_fastapi('crm.lead')</field>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
        <field name="doall" eval="False" />
    </record>

    <!-- Scheduled action cho đồng bộ hóa hàng ngày các master data -->
    <record id="th_schedule_sync_fastapi_admissions_region_daily" model="ir.cron">
        <field name="name">Schedule sync Admissions Region Daily</field>
        <field name="model_id" ref="model_th_clipboard"/>
        <field name="state">code</field>
        <field name="code">model.search([('th_model_name', '=', 'th.admissions.region'), ('th_type_sync', '=', 'a_day'), ('th_status', '=', 'waiting'), ('th_system', '=', 'b2b')]).schedule_sync_fastapi('th.admissions.region')</field>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
        <field name="doall" eval="False" />
    </record>

    <record id="th_schedule_sync_fastapi_admissions_station_daily" model="ir.cron">
        <field name="name">Schedule sync Admissions Station Daily</field>
        <field name="model_id" ref="model_th_clipboard"/>
        <field name="state">code</field>
        <field name="code">model.search([('th_model_name', '=', 'th.admissions.station'), ('th_type_sync', '=', 'a_day'), ('th_status', '=', 'waiting'), ('th_system', '=', 'b2b')]).schedule_sync_fastapi('th.admissions.station')</field>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
        <field name="doall" eval="False" />
    </record>

    <record id="th_schedule_sync_fastapi_major_daily" model="ir.cron">
        <field name="name">Schedule sync Major Daily</field>
        <field name="model_id" ref="model_th_clipboard"/>
        <field name="state">code</field>
        <field name="code">model.search([('th_model_name', '=', 'th.major'), ('th_type_sync', '=', 'a_day'), ('th_status', '=', 'waiting'), ('th_system', '=', 'b2b')]).schedule_sync_fastapi('th.major')</field>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
        <field name="doall" eval="False" />
    </record>

    <record id="th_schedule_sync_fastapi_graduation_system_daily" model="ir.cron">
        <field name="name">Schedule sync Graduation System Daily</field>
        <field name="model_id" ref="model_th_clipboard"/>
        <field name="state">code</field>
        <field name="code">model.search([('th_model_name', '=', 'th.graduation.system'), ('th_type_sync', '=', 'a_day'), ('th_status', '=', 'waiting'), ('th_system', '=', 'b2b')]).schedule_sync_fastapi('th.graduation.system')</field>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
        <field name="doall" eval="False" />
    </record>

   

    <record id="th_schedule_sync_fastapi_crm_tag_daily" model="ir.cron">
        <field name="name">Schedule sync CRM Tag Daily</field>
        <field name="model_id" ref="model_th_clipboard"/>
        <field name="state">code</field>
        <field name="code">model.search([('th_model_name', '=', 'crm.tag'), ('th_type_sync', '=', 'a_day'), ('th_status', '=', 'waiting'), ('th_system', '=', 'b2b')]).schedule_sync_fastapi('crm.tag')</field>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
        <field name="doall" eval="False" />
    </record>
</odoo>
