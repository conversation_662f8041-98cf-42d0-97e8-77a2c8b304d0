<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="0">
    <record id="th_odoo_trigger_crm_create" model="base.automation">
        <field name="name">Base Automation: Create or Write CRM</field>
        <field name="model_id" ref="model_crm_lead"/>
        <field name="state">code</field>
        <field name="code">
if not env.context.get('th_sync'):
    record.th_trigger_crm_lead(record, 'create',th_type_sync='an_hour')
        </field>
        <field name="trigger_field_ids" eval="[(6, 0, [
            ref('th_crm.field_crm_lead__th_ownership_id'),
            ref('th_crm.field_crm_lead__th_origin_id'),
            ref('th_crm.field_crm_lead__th_source_name'),
            ref('th_crm.field_crm_lead__th_description'),
            ref('th_crm.field_crm_lead__th_major_id'),
            ref('crm.field_crm_lead__type'),
            ref('th_crm.field_crm_lead__th_status_group_id'),
            ref('th_crm.field_crm_lead__th_status_detail_id'),
            ref('th_crm.field_crm_lead__th_channel_id'),
            ref('th_crm.field_crm_lead__th_source_group_id'),
            ref('crm.field_crm_lead__stage_id'),
            ref('th_crm.field_crm_lead__state'),
            ref('th_crm.field_crm_lead__th_last_check'),
            ref('crm.field_crm_lead__partner_id'),
            ref('th_crm.field_crm_lead__th_partner_referred_id'),
            ref('th_crm.field_crm_lead__th_admissions_station_id'),
            ref('th_crm.field_crm_lead__th_admissions_region_id'),
            ref('th_crm.field_crm_lead__th_level_up_date'),
            ref('th_crm.field_crm_lead__th_graduation_system_id'),
            ref('th_crm.field_crm_lead__th_utm_source'),
            ref('th_crm.field_crm_lead__th_utm_medium'),
            ref('th_crm.field_crm_lead__th_utm_campaign'),
            ref('th_crm.field_crm_lead__th_utm_content'),
            ref('th_crm.field_crm_lead__th_utm_term'),
            ref('th_crm.field_crm_lead__th_crm_job'),
            ref('crm.field_crm_lead__user_id'),
            ref('th_crm.field_crm_lead__th_self_lead'),
        ])]" />
        <field name="trigger">on_create</field>
        <field name="active">1</field>
    </record>

<!--    <record id="th_odoo_trigger_crm_update_ontime" model="base.automation">-->
<!--        <field name="name">Base Automation: Write CRM On Time</field>-->
<!--        <field name="model_id" ref="model_crm_lead"/>-->
<!--        <field name="state">code</field>-->
<!--        <field name="code">if not env.context.get('th_sync') and record.id != False: record.th_trigger_crm_lead(record, 'update', th_type_sync='ontime')</field>-->
<!--        <field name="trigger_field_ids" eval="[(6, 0, [-->
<!--            ref('th_crm.field_crm_lead__th_ownership_id'),-->
<!--            ref('th_crm.field_crm_lead__th_status_group_id'),-->
<!--            ref('th_crm.field_crm_lead__th_status_detail_id'),-->
<!--            ref('th_crm.field_crm_lead__th_origin_id'),-->
<!--            ref('th_crm.field_crm_lead__th_last_check'),-->
<!--            ref('crm.field_crm_lead__stage_id'),-->
<!--            ref('crm.field_crm_lead__partner_id'),-->
<!--        ])]" />-->
<!--        <field name="trigger">on_write</field>-->
<!--        <field name="active">1</field>-->
<!--    </record>-->

    <record id="th_odoo_trigger_crm_update_an_hour" model="base.automation">
        <field name="name">Base Automation: Write CRM An Hour</field>
        <field name="model_id" ref="model_crm_lead"/>
        <field name="state">code</field>
        <field name="code">
if not env.context.get('th_sync'):
    record.th_trigger_crm_lead(record, 'update', th_type_sync='an_hour')
        </field>
        <field name="trigger_field_ids" eval="[(6, 0, [
            ref('th_crm.field_crm_lead__th_source_name'),
            ref('th_crm.field_crm_lead__th_description'),
            ref('th_crm.field_crm_lead__th_major_id'),
            ref('crm.field_crm_lead__type'),
            ref('th_crm.field_crm_lead__th_channel_id'),
            ref('th_crm.field_crm_lead__th_source_group_id'),
            ref('th_crm.field_crm_lead__state'),
            ref('crm.field_crm_lead__partner_id'),
            ref('th_crm.field_crm_lead__th_partner_referred_id'),
            ref('th_crm.field_crm_lead__th_admissions_station_id'),
            ref('th_crm.field_crm_lead__th_admissions_region_id'),
            ref('th_crm.field_crm_lead__th_storage'),
            ref('th_crm.field_crm_lead__th_level_up_date'),
            ref('th_crm.field_crm_lead__th_graduation_system_id'),
            ref('th_crm.field_crm_lead__th_utm_source'),
            ref('th_crm.field_crm_lead__th_utm_medium'),
            ref('th_crm.field_crm_lead__th_utm_campaign'),
            ref('th_crm.field_crm_lead__th_utm_content'),
            ref('th_crm.field_crm_lead__th_utm_term'),
            ref('th_crm.field_crm_lead__th_crm_job'),
            ref('crm.field_crm_lead__user_id'),
            ref('th_crm.field_crm_lead__th_self_lead'),
            ref('th_crm.field_crm_lead__th_ownership_id'),
            ref('th_crm.field_crm_lead__th_status_group_id'),
            ref('th_crm.field_crm_lead__th_status_detail_id'),
            ref('th_crm.field_crm_lead__th_origin_id'),
            ref('th_crm.field_crm_lead__th_last_check'),
            ref('crm.field_crm_lead__stage_id'),
        ])]" />
        <field name="trigger">on_write</field>
        <field name="active">1</field>
    </record>

    <record id="th_odoo_trigger_crm_delete_an_hour" model="base.automation">
        <field name="name">Base Automation: Delete CRM An Hour</field>
        <field name="model_id" ref="model_crm_lead"/>
        <field name="state">code</field>
        <field name="code">if not env.context.get('th_sync'): record.th_trigger_crm_lead(record, 'delete', th_type_sync='an_hour')</field>
        <field name="trigger">on_unlink</field>
        <field name="active">1</field>
    </record>


    <record id="th_odoo_trigger_dividing_ring_create" model="base.automation">
        <field name="name">Base Automation: Create or Write Dividing Ring</field>
        <field name="model_id" ref="model_th_dividing_ring"/>
        <field name="state">code</field>
        <field name="code">if not env.context.get('th_sync'): record.th_trigger_dividing_ring(record, 'create')</field>
        <field name="trigger_field_ids" eval="[(6, 0, [
            ref('th_crm.field_th_dividing_ring__name'),
            ref('th_crm.field_th_dividing_ring__th_origin_id'),
            ref('th_crm.field_th_dividing_ring__th_is_partner_dividing'),
        ])]" />
        <field name="trigger">on_create</field>
        <field name="active">1</field>
    </record>
     <record id="th_odoo_trigger_dividing_ring_update" model="base.automation">
        <field name="name">Base Automation: Create or Write Dividing Ring</field>
        <field name="model_id" ref="model_th_dividing_ring"/>
        <field name="state">code</field>
        <field name="code">if not env.context.get('th_sync'): record.th_trigger_dividing_ring(record, 'update')</field>
        <field name="trigger_field_ids" eval="[(6, 0, [
            ref('th_crm.field_th_dividing_ring__name'),
            ref('th_crm.field_th_dividing_ring__th_origin_id'),
            ref('th_crm.field_th_dividing_ring__th_is_partner_dividing'),
        ])]" />
        <field name="trigger">on_write</field>
        <field name="active">1</field>
    </record>

    <record id="th_odoo_trigger_dividing_ring_delete" model="base.automation">
        <field name="name">Base Automation: Delete Dividing Ring</field>
        <field name="model_id" ref="model_th_dividing_ring"/>
        <field name="state">code</field>
        <field name="code">if not env.context.get('th_sync'): record.th_trigger_dividing_ring(record, 'delete')</field>
        <field name="trigger">on_unlink</field>
        <field name="active">1</field>
    </record>


    <record id="th_odoo_trigger_crm_stage_update" model="base.automation">
        <field name="name">Base Automation: Create or Write CRM Stage</field>
        <field name="model_id" ref="model_crm_stage"/>
        <field name="state">code</field>
        <field name="code">
if not env.context.get('th_sync'):
    record.th_trigger_crm_stage(record, 'create_or_update')
        </field>
        <field name="trigger_field_ids" eval="[(6, 0, [
            ref('crm.field_crm_stage__name'),
            ref('crm.field_crm_stage__is_won'),
            ref('crm.field_crm_stage__requirements'),
            ref('th_crm.field_crm_stage__th_type'),
            ref('th_crm.field_crm_stage__th_auto'),
            ref('th_crm.field_crm_stage__th_required_fill'),
        ])]" />
        <field name="trigger">on_create_or_write</field>
        <field name="active">1</field>
    </record>

    <record id="th_odoo_trigger_crm_stage_delete" model="base.automation">
        <field name="name">Base Automation: Delete CRM Stage</field>
        <field name="model_id" ref="model_crm_stage"/>
        <field name="state">code</field>
        <field name="code">
if not env.context.get('th_sync'):
    record.th_trigger_crm_stage(record, 'delete')
        </field>
        <field name="trigger">on_unlink</field>
        <field name="active">1</field>
    </record>

    <!-- Automation cho đồng bộ hóa hàng ngày các trường CRM -->
    <record id="th_odoo_trigger_crm_daily_sync" model="base.automation">
        <field name="name">Base Automation: CRM Daily Sync</field>
        <field name="model_id" ref="model_crm_lead"/>
        <field name="state">code</field>
        <field name="code">
if not env.context.get('th_sync'):
    record.th_trigger_crm_lead_daily_sync(record, 'create_or_update')
        </field>
        <field name="trigger_field_ids" eval="[(6, 0, [
            ref('th_crm.field_crm_lead__th_admissions_region_id'),
            ref('th_crm.field_crm_lead__th_admissions_station_id'),
            ref('th_crm.field_crm_lead__th_major_id'),
            ref('th_crm.field_crm_lead__th_graduation_system_id'),
            ref('th_crm.field_crm_lead__th_training_system_id'),
            ref('crm.field_crm_lead__tag_ids'),
            ref('th_crm.field_crm_lead__th_crm_job'),
        ])]" />
        <field name="trigger">on_write</field>
        <field name="active">1</field>
    </record>

    <!-- Automation cho các master data -->

    <record id="th_odoo_trigger_training_system_daily" model="base.automation">
        <field name="name">Base Automation: Training System Daily Sync</field>
        <field name="model_id" ref="model_th_training_system"/>
        <field name="state">code</field>
        <field name="code">
if not env.context.get('th_sync'):
    record.th_trigger_training_system(record, 'create_or_update')
        </field>
        <field name="trigger_field_ids" eval="[(6, 0, [
            ref('th_crm.field_th_training_system__name'),
            ref('th_crm.field_th_training_system__th_description'),
        ])]" />
        <field name="trigger">on_create_or_write</field>
        <field name="active">1</field>
    </record>

    <!-- <record id="th_odoo_trigger_crm_tag_daily" model="base.automation">
        <field name="name">Base Automation: CRM Tag Daily Sync</field>
        <field name="model_id" ref="model_crm_tag"/>
        <field name="state">code</field>
        <field name="code">
    if not env.context.get('th_sync'):
        record.th_trigger_crm_tag(record, 'create_or_update')
        </field>
        <field name="trigger_field_ids" eval="[(6, 0, [
            ref('crm.field_crm_tag__name'),
            ref('crm.field_crm_tag__color'),
        ])]" />
        <field name="trigger">on_create_or_write</field>
        <field name="active">1</field>
    </record> -->
</odoo>
