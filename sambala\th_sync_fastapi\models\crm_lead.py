from odoo import api, fields
from odoo.models import BaseModel

mapping_key_model = {
    'partner_id': 'res.partner',
    'th_channel_id': 'th.info.channel',
    'th_origin_id': 'th.origin',
    'th_source_group_id': 'th.source.group',
    'th_status_detail_id': 'th.status.detail',
    'th_status_group_id': 'th.status.category',
    'th_admissions_station_id': 'th.admissions.station',
    'th_admissions_region_id': 'th.admissions.region',
    'th_graduation_system_id': 'th.graduation.system',
    'th_training_system_id': 'th.training.system',
    'th_ownership_id': 'th.ownership.unit',
    'th_major_id': 'th.major',
    'stage_id': 'crm.stage',
    'th_dividing_ring_id': 'th.dividing.ring',
    'th_partner_referred_id': 'res.partner',
}

class CrmLead(BaseModel):
    _name = "crm.lead"
    _inherit = ["crm.lead", "th.intermediate.table"]

    def map_data_from_sync(self, data_send):
        data = data_send.copy()
        data.update({
            key: self.sudo()._find_internal_id(th_external_id=data_send[key], th_model_name=model_name)
            for key, model_name in mapping_key_model.items()
            if key in data_send
        })
        return data


    def write(self, values):
        res = super(CrmLead, self).write(values)
        return res

    def get_data_sync(self, rec, action, th_type_sync='ontime'):
        data = {
            'name': rec.name if rec.name else False,
            'th_ownership_id': rec.th_ownership_id.id if rec.th_ownership_id else False,
            'th_origin_id': rec.th_origin_id.id if rec.th_origin_id else False,
            'th_source_name': rec.th_source_name if rec.th_source_name else False,
            'th_description': rec.th_description if rec.th_description else False,
            'th_major_id': rec.th_major_id.id if rec.th_major_id else False,
            'type': 'opportunity',
            'th_status_group_id': rec.th_status_group_id.id if rec.th_status_group_id else False,
            'th_status_detail_id': rec.th_status_detail_id.id if rec.th_status_detail_id else False,
            'th_channel_id': rec.th_channel_id.id if rec.th_channel_id else False,
            'th_source_group_id': rec.th_source_group_id.id if rec.th_source_group_id else False,
            'stage_id': rec.stage_id.id if rec.stage_id else False,
            'state': rec.state if rec.state else False,
            'th_last_check': rec.th_last_check.isoformat() if rec.th_last_check else False,
            'th_partner_referred_id': rec.th_partner_referred_id.id if rec.th_partner_referred_id else False,
            'th_admissions_station_id': rec.th_admissions_station_id.id if rec.th_admissions_station_id else False,
            'th_admissions_region_id': rec.th_admissions_region_id.id if rec.th_admissions_region_id else False,
            'th_level_up_date': rec.th_level_up_date.isoformat() if rec.th_level_up_date else False,
            'th_graduation_system_id': rec.th_graduation_system_id.id if rec.th_graduation_system_id else False,
            'th_training_system_id': rec.th_training_system_id.id if rec.th_training_system_id else False,
            'th_utm_source': rec.th_utm_source,
            'th_utm_medium': rec.th_utm_medium,
            'th_utm_campaign': rec.th_utm_campaign,
            'th_utm_term': rec.th_utm_term,
            'th_utm_content': rec.th_utm_content,
            'th_crm_job': rec.th_crm_job,
            'tag_ids': rec.tag_ids.ids if rec.tag_ids else [],
            'partner_id': rec.partner_id.id if rec.partner_id else False,
            'th_self_lead': rec.th_self_lead,
            'th_dividing_ring_id': rec.th_dividing_ring_id.id if rec.th_dividing_ring_id else False,
            'user_id': rec.user_id.id if rec.user_id else False
        }

        if action == 'update':
            automation = self.env.ref('th_sync_fastapi.th_odoo_trigger_crm_update_'+th_type_sync)
            if not automation:
                return None
            trigger_field_names = automation.trigger_field_ids.mapped('name')
            data = {field: data[field] for field in trigger_field_names if field in data}

        data_send = data.copy()
        if 'user_id' in data_send:
            data_send.pop('user_id')
            data_send['th_person_in_charge'] = rec.user_id.name if rec.user_id else False

        def get_value_external_id(field_name, model_name):
            field_value = getattr(rec, field_name, None)
            if field_value:
                return self.sudo()._find_external_id(th_internal_id=field_value.id, th_model_name=model_name)
            return False

        data_send.update({
            key: get_value_external_id(key, model_name)
            for key, model_name in mapping_key_model.items()
            if key in data
        })

        # Xử lý tag_ids (Many2many field)
        if 'tag_ids' in data and data['tag_ids']:
            th_tag_external_ids = []
            for tag_id in data['tag_ids']:
                th_external_id = self.sudo()._find_external_id(th_internal_id=tag_id, th_model_name='crm.tag')
                if th_external_id:
                    th_tag_external_ids.append(th_external_id)
            data_send['tag_ids'] = th_tag_external_ids

        if not data_send['partner_id'] and action == 'create': # partner mới
            partner = rec.partner_id
            data['partner_info'] = {
                'th_customer_code': partner.th_customer_code if partner.th_customer_code else False,
                'name': partner.name if partner.name else False,
                'phone': partner.phone if partner.phone else False,
                'th_phone2': partner.th_phone2 if partner.th_phone2 else False,
                'email': partner.email if partner.email else False,
                'th_gender': partner.th_gender if partner.th_gender else False,
                'th_birthday': partner.th_birthday.isoformat() if partner.th_birthday else False,
                'title': partner.title.id if partner.title else False,
                'function': partner.function if partner.function else False,
                'th_citizen_identification': partner.th_citizen_identification if partner.th_citizen_identification else False,
                'th_date_identification': partner.th_date_identification.isoformat() if partner.th_date_identification else False,
                'th_place_identification': partner.th_place_identification if partner.th_place_identification else False,
                'vat': partner.vat if partner.vat else False,
                'lang': partner.lang if partner.lang else False,
                # liên hệ
                'street': partner.street if partner.street else False,
                'th_ward_id': partner.th_ward_id.id if partner.th_ward_id else False,
                'th_district_id': partner.th_district_id.id if partner.th_district_id else False,
                'state_id': partner.state_id.id if partner.state_id else False,
                'country_id': partner.country_id.id if partner.country_id else False,
                # địa chỉ thường trú
                'th_street': partner.th_street if partner.th_street else False,
                'th_ward_permanent_id': partner.th_ward_permanent_id.id if partner.th_ward_permanent_id else False,
                'th_district_permanent_id': partner.th_district_permanent_id.id if partner.th_district_permanent_id else False,
                'th_state_id': partner.th_state_id.id if partner.th_state_id else False,
                'th_country_id': partner.th_country_id.id if partner.th_country_id else False,

                'th_place_of_birth_id': partner.th_place_of_birth_id.id if partner.th_place_of_birth_id else False,
                'th_ethnicity_id': partner.th_ethnicity_id.id if partner.th_ethnicity_id else False,
                'th_religion_id': partner.th_religion_id.id if partner.th_religion_id else False,
                'th_module_ids': partner.th_module_ids.mapped('th_module_b2b_id') if partner.th_module_ids else False,
            }
            data_send['partner_info'] = data['partner_info'].copy()
        return data, data_send

    @api.model
    def th_trigger_crm_lead(self, records, action, th_type_sync):
        for rec in records:
            mapping = self.env['th.mapping.id'].sudo().search(
                [('th_internal_id', '=', rec.id), ('th_model_name', '=', 'crm.lead')], limit=1)
            if rec.th_ownership_id.th_is_sync == True or mapping:
                if mapping:
                    if not rec._context.get('old_values', {}):
                        continue
                    old_values = rec._context.get('old_values', {}).values()
                    th_data = self.optimal_data(rec, list(old_values))
                    val = {
                        'name': rec.name,
                        'th_type_sync': th_type_sync,
                        'th_internal_id': rec.id,
                        'th_data': th_data,
                        'th_system': 'b2b',
                    }
                    if action == 'update':
                        val['th_data_send'] = th_data[0]
                        self.sudo().th_func_create_or_update(
                            val)
                else:
                    th_data, th_data_send = self.get_data_sync(rec, action, th_type_sync)
                    if self._context.get('duplicate_arbitration', False):
                        th_data.update({
                            'th_is_a_duplicate_opportunity': False,
                            'th_duplicate_type': 'manual',
                            'th_duplicate_processed_lead': True,
                            'th_crm_lead_arbitrate_lose': self._context.get('th_crm_lead_arbitrate_lose', False).name
                        })
                        th_data_send.update({
                            'th_is_a_duplicate_opportunity': False,
                            'th_duplicate_type': 'manual',
                            'th_duplicate_processed_lead': True,
                            'th_crm_lead_arbitrate_lose': self._context.get('th_crm_lead_arbitrate_lose', False).name
                        })
                    val = {
                        'name': rec.name,
                        'th_type_sync': th_type_sync,
                        'th_internal_id': rec.id,
                        'th_data': th_data_send,
                        'th_module_id': self.env.ref('th_setup_parameters.th_crm_module').id,
                        'th_system': 'b2b',
                    }
                    if action == 'delete':
                        th_type_sync = 'an_hour'
                        self.sudo().th_func_delete(val)
                    elif th_data_send:
                        val['th_data_send'] = th_data_send
                        self.sudo().th_func_create_or_update(val)

    @api.model
    def th_trigger_crm_lead_daily_sync(self, records, action):
        """
        Đồng bộ hóa hàng ngày cho các trường:
        - th_admissions_region_id (Vùng tuyển sinh)
        - th_admissions_station_id (Trạm tuyển sinh)
        - th_major_id (Ngành đăng ký)
        - th_graduation_system_id (Hệ tốt nghiệp)
        - th_training_system_id (Hệ đào tạo)
        - tag_ids (Nhóm cơ hội)
        - th_crm_job (Nghề nghiệp)
        """
        for rec in records:
            mapping = self.env['th.mapping.id'].sudo().search(
                [('th_internal_id', '=', rec.id), ('th_model_name', '=', 'crm.lead')], limit=1)
            if rec.th_ownership_id.th_is_sync == True or mapping:
                # Chỉ đồng bộ các trường được yêu cầu
                th_daily_fields = {
                    'th_admissions_region_id': rec.th_admissions_region_id.id if rec.th_admissions_region_id else False,
                    'th_admissions_station_id': rec.th_admissions_station_id.id if rec.th_admissions_station_id else False,
                    'th_major_id': rec.th_major_id.id if rec.th_major_id else False,
                    'th_graduation_system_id': rec.th_graduation_system_id.id if rec.th_graduation_system_id else False,
                    'th_training_system_id': rec.th_training_system_id.id if rec.th_training_system_id else False,
                    'tag_ids': rec.tag_ids.ids if rec.tag_ids else [],
                    'th_crm_job': rec.th_crm_job,
                }

                th_data_send = th_daily_fields.copy()

                # Mapping external IDs cho các trường Many2one
                def get_value_external_id(field_name, model_name):
                    field_value = getattr(rec, field_name, None)
                    if field_value:
                        return self.sudo()._find_external_id(th_internal_id=field_value.id, th_model_name=model_name)
                    return False

                th_data_send.update({
                    key: get_value_external_id(key, model_name)
                    for key, model_name in mapping_key_model.items()
                    if key in th_daily_fields and th_daily_fields[key]
                })

                # Xử lý tag_ids (Many2many field)
                if th_daily_fields['tag_ids']:
                    th_tag_external_ids = []
                    for tag_id in th_daily_fields['tag_ids']:
                        th_external_id = self.sudo()._find_external_id(th_internal_id=tag_id, th_model_name='crm.tag')
                        if th_external_id:
                            th_tag_external_ids.append(th_external_id)
                    th_data_send['tag_ids'] = th_tag_external_ids

                val = {
                    'name': rec.name,
                    'th_type_sync': 'a_day',
                    'th_internal_id': rec.id,
                    'th_data': th_daily_fields,
                    'th_data_send': th_data_send,
                    'th_system': 'b2b',
                }

                if action == 'create_or_update' and th_data_send:
                    self.sudo().th_func_create_or_update(val)
                elif action == 'delete':
                    self.sudo().th_func_delete(val)
