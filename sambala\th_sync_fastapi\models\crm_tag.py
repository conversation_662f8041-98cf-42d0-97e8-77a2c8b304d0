from odoo import api, fields, models


class CrmTag(models.Model):
    _name = "crm.tag"
    _inherit = ["crm.tag", "th.intermediate.table"]

    def get_data_sync(self, rec):
        data = {
            'name': rec.name if rec.name else False,
            'color': rec.color if rec.color else 0,
        }
        return data, data.copy()

    @api.model
    def th_trigger_crm_tag(self, records, action, th_type_sync='ontime'):
        for rec in records:
            th_data, th_data_send = self.get_data_sync(rec)
            val = {
                'name': rec.name,
                'th_type_sync': th_type_sync,
                'th_internal_id': rec.id,
                'th_data': th_data,
                'th_system': 'b2b',
            }
            if action == 'create_or_update':
                val['th_data_send'] = th_data_send
                self.sudo().th_func_create_or_update(val)
            elif action == 'delete':
                self.sudo().th_func_delete(val)
