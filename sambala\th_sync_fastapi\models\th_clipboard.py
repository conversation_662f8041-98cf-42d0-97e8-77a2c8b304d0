import requests
from odoo import api, models, fields, _
from odoo.exceptions import ValidationError, UserError


class ThClipboard(models.Model):

    _inherit = "th.clipboard"

    def action_call_api_sync(self):
        for rec in self:
            rec.sudo().call_api_sync(rec)

    @api.model
    def create(self, values):
        res = super(ThClipboard, self).create(values)
        try:
            for rec in res:
                if rec.th_type_sync == "ontime":
                    rec.sudo().call_api_sync(rec)
        except ValidationError as e:
            raise ValidationError(e)
        return res

    @staticmethod
    def sync_fastapi(env, system, endpoint, method, data=None, record_id=None):
        th_api = env['th.api.server'].search([('state', '=', 'deploy'), ('th_type', '=', system)], limit=1, order='id desc')
        if not th_api:
            raise UserError("No active API server configuration found for 'samp'.")

        headers = {
            "api-key": th_api.th_api_key or "",
            "Content-Type": "application/json"
        }

        url = f"{th_api.th_url_api}{th_api.th_api_root}/api/{endpoint}/{record_id or ''}"
        try:
            response = requests.request(method=method, url=url, json=data, headers=headers)
            response.raise_for_status()
            if response.status_code in [200, 201]:
                action_method_dict = {
                    'POST': 'tạo',
                    'PUT': 'sửa',
                    'DELETE': 'xóa',
                }
                msg = f"Đã đồng bộ {action_method_dict[method]} {endpoint} sang hệ thống B2B"
                env['bus.bus']._sendone(
                    env.user.partner_id,
                    "simple_notification",
                    {
                        "title": "Thông báo",
                        "message": msg,
                        "sticky": False,
                        "warning": False
                    }
                )
            return response.json()
        except requests.HTTPError as e:
            try:
                error_content = response.json()
            except ValueError:
                error_content = "No response content"
            raise UserError(f"Failed to sync: {e}, Details: {error_content}")

    def get_config_router(self, model_name):
        model_config = {
            "crm.lead": {
                "endpoint": "crmlead",
                "endpoint_multi": "updatecrm",
                "custom_response": self._process_crm_lead
            },
            "th.source.group": {
                "endpoint": "sourcegroup",
            },
            "th.graduation.system": {
                "endpoint": "graduationsystem",
            },
            "product.category": {
                "endpoint": "productcategory",
            },
            "th.info.channel": {
                "endpoint": "infochannel",
            },
            "th.country.district": {
                "endpoint": "countrydistrict",
            },
            "th.country.ward": {
                "endpoint": "countryward",
            },
            "th.major": {
                "endpoint": "major",
            },
            "th.admissions.station": {
                "endpoint": "admissionsstation",
            },
            "th.admissions.region": {
                "endpoint": "admissionsregion",
            },
            "th.status.detail": {
                "endpoint": "statusdetail",
            },
            "th.status.category": {
                "endpoint": "statuscategory",
            },
            "th.ownership.unit": {
                "endpoint": "ownershipunit",
            },
            "th.origin": {
                "endpoint": "origin",
            },
            "th.exempted.subject": {
                "endpoint": "exemptedsubject",
            },
            "th.internship.conditions": {
                "endpoint": "internshipconditions",
            },
            "th.apm.level": {
                "endpoint": "apmlevel",
            },
            "product.template": {
                "endpoint": "producttemplate",
                "custom_response": self._process_product_template
            },
            "th.status.student.particular": {
                "endpoint": "statusstudentparticular",
            },
            "th.fee.status.particular": {
                "endpoint": "statusstudentfee",
            },
            "res.country.state": {
                "endpoint": "countrystate",
            },
            "th.student.profile": {
                "endpoint": "studentprofile",
            },
            "mail.message": {
                "endpoint": "mailmessage",
                "endpoint_multi": "mailmessages",
            },
            "th.dividing.ring": {
                "endpoint": "dividingring",
            },
            "th.care.history": {
                "endpoint": "carehistory",
            },
            "crm.stage": {
                "endpoint": "crmstage",
            },
            "th.apm.campaign": {
                "endpoint": "apmcampaign",
            },
            "res.partner": {
                "endpoint": "contact",
                "endpoint_multi": "contact",
            },
            "th.apm.trait": {
                "endpoint": "thapmtrait",
                "endpoint_multi": "thapmtrait",
            },
            "th.apm.trait.value": {
                "endpoint": "thapmtraitvalue",
                "endpoint_multi": "thapmtraitvalue",
            },
            "th.apm.contact.trait": {
                "endpoint": "thapmcontacttrait",
                "endpoint_multi": "thapmcontacttrait",
            },
            "sale.order": {
                "endpoint": "saleorder",
                "endpoint_multi": "saleorders",
                "custom_response": self._process_sale_order
            },
            "th.student": {
                "endpoint": "student",
                "endpoint_multi": "student",
                "custom_response": self._process_th_student
            },
            "th.apm": {
                "endpoint": "apmleads",
                "endpoint_multi": "apmleads",
                "custom_response": self._process_th_apm
            },
            "th.apm.team": {
                "endpoint": "apmteam",
            },
            'product.pricelist': {
                'endpoint': 'pricelist',
                'root_path': '/stt',
                "custom_response": self._process_pricelist
            },
            'product.pricelist.item': {
                'endpoint': 'pricelistitem',
                'root_path': '/stt'
            },
            "th.student.status.detail": {
                "endpoint": "studentstatusdetail",
            },
            "th.student.status": {
                "endpoint": "studentstatus",
            },
            "res.partner.title": {
                "endpoint": "respartnertitle",
            },
            "th.training.system": {
                "endpoint": "trainingsystem",
            },
            "crm.tag": {
                "endpoint": "crmstag",
            },
        }
        return model_config.get(model_name, None)

    def call_api_sync(self, clipboard_data=None):
        def process_record(rec, config):
            """Xử lý chung cho từng bản ghi."""
            try:
                rec.th_status = 'pending'
                response = self.sync_fastapi(
                    self.env,
                    system=rec.th_system,
                    endpoint=config['endpoint'],
                    method=rec.th_method,
                    data=rec.th_data_send,
                    record_id=rec.th_mapping_id.th_external_id
                )

                if response:
                    if 'id' in response:
                        rec.th_mapping_id.th_external_id = response.get('id')
                    if 'custom_response' in config:
                        config['custom_response'](rec, response)

                rec.write({'th_status': 'success', 'th_response_data': str(response) if response else 'OK'})
            except Exception as e:
                rec.write({'th_status': 'error', 'th_response_data': str(e)})

        for rec in clipboard_data:
            config = self.get_config_router(rec.th_model_name)
            if config:
                process_record(rec, config)

    def schedule_sync_fastapi(self, th_model_name, th_system='b2b'):
        def process_multi_record(data, config):
            try:
                # self.th_status = 'pending'

                responses = self.sync_fastapi(
                    self.env,
                    system=th_system,
                    endpoint=config['endpoint_multi'],
                    method='POST',
                    data=data,

                )
                for record, response in zip(self, responses):
                    if response and 'custom_response' in config:
                        config['custom_response'](record, response)
                    if 'id' in response:
                        record.th_mapping_id.write({'th_external_id': response.get('id')})
                    record.write({'th_status': 'success',
                                  'th_response_data': str(response.get('response')) if response.get('response') else 'OK'})
            except Exception as e:
                self.write({'th_status': 'error', 'th_response_data': str(e)})

        config = self.get_config_router(th_model_name)
        if config.get('endpoint_multi', False):
            data = []
            for rec in self:
                if th_model_name == 'sale.order':
                    rec.th_status = 'pending'
                    data.append({
                        'id_b2b': rec.th_mapping_id.th_external_id if rec.th_mapping_id.th_external_id else None,
                        'th_data_sale': rec.th_data_send,
                    })
                elif th_model_name == 'crm.lead':
                    data.append({
                        'id_b2b': rec.th_mapping_id.th_external_id,
                        'th_data_crm': rec.th_data_send,
                    })
                elif th_model_name == 'mail.message' :
                    data.append({
                        'id_b2b': rec.th_mapping_id.th_external_id if rec.th_mapping_id.th_external_id else None,
                        'th_data_mail': rec.th_data_send,
                    })
                # elif th_model_name == 'th.apm.trait':
                #     if rec.th_method == 'DELETE':
                #         data.append({
                #             'id_b2b': rec.th_mapping_id.th_external_id,
                #         })
                #     else:
                #         data.append({
                #             'id_b2b': rec.th_mapping_id.th_external_id,
                #             'th_data_apm_trait': rec.th_data_send,
                #         })
                elif th_model_name == 'th.apm':
                    if rec.th_method == 'DELETE':
                        data.append({
                            'id_b2b': rec.th_mapping_id.th_external_id,
                        })
                    else:
                        data.append({
                            'id_b2b': rec.th_mapping_id.th_external_id,
                            'th_data_apm': rec.th_data_send,
                        })
                elif th_model_name == 'th.student':
                    data.append({
                        'id_b2b': rec.th_mapping_id.th_external_id,
                        'th_data_srm': rec.th_data_send,
                    })
                elif th_model_name == 'res.partner':
                    data.append({
                        'id_b2b': rec.th_mapping_id.th_external_id,
                        'th_data_res_partner': rec.th_data_send,
                    })
            process_multi_record(data, config)

    def schedule_sync_fastapi_a_day(self, th_model_name, th_system='b2b'):
        """
        Đồng bộ dữ liệu cần xóa từ SamP sang B2B 1 lần/ngày.
        """

        def process_record(data, config):
            try:
                if isinstance(self, type(self.env['th.clipboard'])):
                    self.write({'th_status': 'pending'})

                for record_data in data:
                    id_b2b = record_data.get('id_b2b')
                    if not id_b2b:
                        raise ValueError("Không tìm thấy id_b2b trong dữ liệu đồng bộ.")

                    # Gửi yêu cầu DELETE đến API FastAPI
                    response = self.sync_fastapi(
                        self.env,
                        system=th_system,
                        endpoint=config['endpoint'],
                        method='DELETE',
                        data=None,
                        record_id=id_b2b
                    )

                    # Xử lý phản hồi
                    if response.get('status') == 'success':
                        record_data['th_status'] = 'success'
                        record_data['response_data'] = str(response.get('response', 'OK'))
                    else:
                        record_data['th_status'] = 'error'
                        record_data['response_data'] = str(response.get('response', 'Lỗi không xác định'))
            except Exception as e:
                for record_data in data:
                    record_data['th_status'] = 'error'
                    record_data['response_data'] = str(e)

        # Lấy cấu hình endpoint cho model
        config = self.get_config_router(th_model_name)
        if config:
            # Lọc các bản ghi cần xóa (th_type_sync = 'a_day' và th_status = 'waiting')
            records_to_sync = self.search([
                ('th_model_name', '=', th_model_name),
                ('th_type_sync', '=', 'a_day'),
                ('th_status', '=', 'waiting'),
            ])
            data = []
            for rec in records_to_sync:
                # Tìm external_id từ th.mapping.id
                mapping = self.env['th.mapping.id'].search([
                    ('th_internal_id', '=', rec.th_internal_id),
                    ('th_model_name', '=', th_model_name),
                    ('th_system', '=', th_system),
                ], limit=1)

                external_id = mapping.th_external_id if mapping else None
                if not external_id:
                    rec.write({'th_status': 'error',
                               'th_response_data': f"Không tìm thấy external_id cho bản ghi {rec.th_internal_id}."})
                    continue

                data.append({
                    'id_b2b': external_id,
                    'th_data_apm': rec.th_data,
                    'record': rec
                })

            process_record(data, config)

            # Cập nhật trạng thái bản ghi sau khi xử lý
            for record_data in data:
                rec = record_data.get('record')
                if rec and isinstance(rec, type(self.env['th.clipboard'])):
                    rec.write({
                        'th_status': record_data['th_status'],
                        'th_response_data': record_data['response_data']
                    })

    # def schedule_sync_fastapi_a_day(self, th_model_name, th_system='b2b'):
    #     """
    #     Đồng bộ dữ liệu cần xóa từ SamP sang B2B 1 lần/ngày.
    #     """
    #
    #     def process_record(data, config):
    #         """
    #         Xử lý từng bản ghi để gửi yêu cầu DELETE đến FastAPI.
    #         """
    #         try:
    #             for record_data in data:
    #                 id_b2b = record_data.get('id_b2b')
    #                 if not id_b2b:
    #                     raise ValueError("Không tìm thấy id_b2b trong dữ liệu đồng bộ.")
    #
    #                 # Gửi yêu cầu DELETE đến API FastAPI
    #                 response = self.sync_fastapi(
    #                     self.env,
    #                     system=th_system,
    #                     endpoint=config['endpoint'],
    #                     method='DELETE',
    #                     record_id=id_b2b
    #                 )
    #
    #                 # Xử lý phản hồi
    #                 if response.get('status') == 'success':
    #                     record_data['th_status'] = 'success'
    #                     record_data['response_data'] = str(response.get('response', 'OK'))
    #                 else:
    #                     record_data['th_status'] = 'error'
    #                     record_data['response_data'] = str(response.get('response', 'Lỗi không xác định'))
    #         except Exception as e:
    #             for record_data in data:
    #                 record_data['th_status'] = 'error'
    #                 record_data['response_data'] = str(e)
    #
    #     # Lấy cấu hình endpoint cho model
    #     config = self.get_config_router(th_model_name)
    #     if not config:
    #         raise ValueError(f"Không tìm thấy cấu hình endpoint cho model {th_model_name}.")
    #
    #     # Lọc các bản ghi cần đồng bộ
    #     records_to_sync = self.search([
    #         ('th_model_name', '=', th_model_name),
    #         ('th_type_sync', '=', 'a_day'),
    #         ('th_status', '=', 'waiting'),
    #     ])
    #     data = []
    #
    #     for rec in records_to_sync:
    #         # Tìm external_id từ bảng th.mapping.id
    #         mapping = self.env['th.mapping.id'].search([
    #             ('th_internal_id', '=', rec.th_internal_id),
    #             ('th_model_name', '=', th_model_name),
    #             ('th_system', '=', th_system),
    #         ], limit=1)
    #
    #         external_id = mapping.th_external_id if mapping else None
    #
    #         # Nếu external_id chưa tồn tại, tự động tạo mới
    #         if not external_id:
    #             # Gửi yêu cầu tạo mới external_id đến API
    #             create_response = self.sync_fastapi(
    #                 self.env,
    #                 system=th_system,
    #                 endpoint=config['endpoint'],
    #                 method='POST',
    #                 data={'th_data': rec.th_data_send}
    #             )
    #
    #             if create_response.get('status') == 'success' and 'id' in create_response:
    #                 external_id = create_response['id']
    #                 # Lưu mapping vào bảng th.mapping.id
    #                 self.env['th.mapping.id'].create({
    #                     'th_internal_id': rec.th_internal_id,
    #                     'th_external_id': external_id,
    #                     'th_model_name': th_model_name,
    #                     'th_system': th_system,
    #                 })
    #             else:
    #                 rec.write({
    #                     'th_status': 'error',
    #                     'th_response_data': f"Lỗi khi tạo external_id: {create_response.get('response', 'Không xác định')}"
    #                 })
    #                 continue
    #
    #         # Thêm bản ghi vào danh sách xử lý
    #         data.append({
    #             'id_b2b': external_id,
    #             'th_data_apm': rec.th_data,
    #             'record': rec
    #         })
    #
    #     # Xử lý đồng bộ các bản ghi
    #     process_record(data, config)
    #
    #     # Cập nhật trạng thái sau khi xử lý
    #     for record_data in data:
    #         rec = record_data.get('record')
    #         if rec:
    #             rec.write({
    #                 'th_status': record_data['th_status'],
    #                 'th_response_data': record_data['response_data']
    #             })

    def _process_crm_lead(self, rec, response):
        """Xử lý logic riêng cho crm.lead."""
        if response.get('partner_id', None):
            partner_id = rec.th_data.get('partner_id')
            mapping_partner = self.env['th.mapping.id'].search([
                ('th_model_name', '=', 'res.partner'),
                ('th_internal_id', '=', partner_id)
            ], limit=1)
            if not mapping_partner:
                mapping_partner = self.env['th.mapping.id'].create({
                    'name': rec.th_data.get('partner_info').get('name'),
                    'th_model_name': 'res.partner',
                    'th_internal_id': partner_id,
                    'th_system': 'b2b'
                })
            mapping_partner.th_external_id = response.get('partner_id')
    def _process_th_student(self, rec, response):
        """Xử lý logic riêng cho th.student"""
        if response.get('th_partner_id', None):
            partner_id = rec.th_data.get('th_partner_id')
            mapping_partner = self.env['th.mapping.id'].search([
                ('th_model_name', '=', 'res.partner'),
                ('th_internal_id', '=', partner_id)
            ], limit=1)
            if not mapping_partner:
                mapping_partner = self.env['th.mapping.id'].create({
                    'name': rec.th_data.get('partner_info').get('name'),
                    'th_model_name': 'res.partner',
                    'th_internal_id': partner_id,
                    'th_system': 'b2b'
                })
            mapping_partner.th_external_id = response.get('th_partner_id')

    def _process_product_template(self, rec, response):
        """Xử lý logic riêng cho product.template."""
        if response.get('th_product_product_b2b_ids', None):
            product_ids = rec.th_data.get('th_product_product_samp_ids')
            product_b2b_ids = response.get('th_product_product_b2b_ids')
            for product_id, product_b2b_id in zip(product_ids, product_b2b_ids):
                mapping_product = self.env['th.mapping.id'].search([
                    ('th_model_name', '=', 'product.product'),
                    ('th_internal_id', '=', product_id)
                ], limit=1)
                if not mapping_product:
                    mapping_product = self.env['th.mapping.id'].create({
                        'th_model_name': 'product.product',
                        'th_internal_id': product_id,
                        'th_system': 'b2b'
                    })
                mapping_product.th_external_id = product_b2b_id

    def _process_th_apm(self, rec, response):
        """Xử lý logic riêng cho th.apm"""
        if response.get('th_partner_id', None):
            partner_id = rec.th_data.get('th_partner_id')
            mapping_partner = self.env['th.mapping.id'].search([
                ('th_model_name', '=', 'res.partner'),
                ('th_internal_id', '=', partner_id)
            ], limit=1)
            if not mapping_partner:
                mapping_partner = self.env['th.mapping.id'].create({
                    'name': rec.th_data.get('partner_info').get('name'),
                    'th_model_name': 'res.partner',
                    'th_internal_id': partner_id,
                    'th_system': 'b2b'
                })
            mapping_partner.th_external_id = response.get('th_partner_id')

    def _process_sale_order(self, rec, response):
        """Xử lý logic riêng cho sale.order."""
        if response.get('id', None):
            order =  self.env['th.mapping.id'].search([
                    ('th_model_name', '=', 'sale.order'),
                    ('th_internal_id', '=', rec.th_internal_id)
                ], limit=1).write({'th_external_id': response.get('id')})
        if response.get('line_ids', None):
            order_line_ids = self.env['sale.order'].browse(rec.th_internal_id).order_line.ids
            order_line_b2b_ids = response.get('line_ids')
            for order_line_id, order_line_b2b_id in zip(order_line_ids, order_line_b2b_ids):
                oline = self.env['th.mapping.id'].search([
                    ('th_model_name', '=', 'sale.order.line'),
                    ('th_internal_id', '=', order_line_id)
                ], limit=1)
                if oline: oline.unlink()
                self.env['th.mapping.id'].create({
                    'th_model_name': 'sale.order.line',
                    'th_internal_id': order_line_id,
                    'th_external_id': order_line_b2b_id,
                    'th_system': 'b2b'
                })

    def _process_pricelist(self, rec, response):
        if response.get('items', None):
            item_b2b_ids = response.get('items')
            pricelist = self.env['product.pricelist'].browse(rec.th_internal_id).item_ids
            for item, item_b2b_id in zip(pricelist, item_b2b_ids):
                self.env['th.mapping.id'].create({
                    'name': item.name,
                    'th_model_name': 'product.pricelist.item',
                    'th_internal_id': item.id,
                    'th_external_id': item_b2b_id,
                    'th_system': 'b2b'
                })
